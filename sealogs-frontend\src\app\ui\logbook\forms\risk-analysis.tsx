'use client'
// Import React and hooks
import React, { useEffect, useState } from 'react'
import {
    UpdateTowingChecklist,
    CreateTowingChecklist,
    UpdateEventType_Tasking,
    CreateMitigationStrategy,
    CreateRiskFactor,
    UpdateRiskFactor,
} from '@/app/lib/graphQL/mutation'
import {
    TowingChecklist,
    GetRiskFactors,
    CrewMembers_LogBookEntrySection,
} from '@/app/lib/graphQL/query'
import { useLazyQuery, useMutation } from '@apollo/client'
import { useToast } from '@/hooks/use-toast'

import { useSearchParams } from 'next/navigation'
import { getLogBookEntryByID } from '@/app/lib/actions'
import { getPermissions, hasPermission } from '@/app/helpers/userHelper'
import LogBookEntryModel from '@/app/offline/models/logBookEntry'
import TowingChecklistModel from '@/app/offline/models/towingChecklist'
import RiskFactorModel from '@/app/offline/models/riskFactor'
import CrewMembers_LogBookEntrySectionModel from '@/app/offline/models/crewMembers_LogBookEntrySection'
import EventType_TaskingModel from '@/app/offline/models/eventType_Tasking'
import MitigationStrategyModel from '@/app/offline/models/mitigationStrategy'
import { generateUniqueId } from '@/app/offline/helpers/functions'
import { uniqBy } from 'lodash'

// Risk Analysis components
import {
    RiskAnalysisSheet,
    RiskDialog,
    StrategyDialog,
} from '@/components/ui/risk-analysis'

export default function RiskAnalysis({
    selectedEvent = false,
    onSidebarClose,
    logBookConfig,
    currentTrip,
    crewMembers = false,
    towingChecklistID = 0,
    setTowingChecklistID,
    offline = false,
    setAllChecked,
    open = false,
    onOpenChange,
}: {
    selectedEvent: any
    onSidebarClose: any
    logBookConfig: any
    currentTrip: any
    crewMembers: any
    towingChecklistID: number
    setTowingChecklistID: any
    offline?: boolean
    setAllChecked: any
    open?: boolean
    onOpenChange?: (open: boolean) => void
}) {
    const searchParams = useSearchParams()
    const logentryID = parseInt(searchParams.get('logentryID') ?? '0')
    const vesselID = searchParams.get('vesselID') ?? 0
    const [riskAnalysis, setRiskAnalysis] = useState<any>(false)
    const [riskBuffer, setRiskBuffer] = useState<any>(false)
    const [openRiskDialog, setOpenRiskDialog] = useState(false)
    const [currentRisk, setCurrentRisk] = useState<any>(false)
    const [content, setContent] = useState<any>('')
    const [allRisks, setAllRisks] = useState<any>(false)
    const [allRiskFactors, setAllRiskFactors] = useState<any>([])
    const [riskValue, setRiskValue] = useState<any>(null)
    // Using setUpdateStrategy but not reading updateStrategy
    const [, setUpdateStrategy] = useState(false)
    // Unused state variables commented out
    // const [strategyEditor, setstrategyEditor] = useState<any>(false)
    const [openRecommendedstrategy, setOpenRecommendedstrategy] =
        useState(false)
    const [recommendedStratagies, setRecommendedStratagies] =
        useState<any>(false)
    const [currentStrategies, setCurrentStrategies] = useState<any>([])
    // Using setRecommendedstrategy but not reading recommendedstrategy
    const [, setRecommendedstrategy] = useState<any>(false)

    const [logbook, setLogbook] = useState<any>(false)
    const [members, setMembers] = useState<any>(false)

    const [permissions, setPermissions] = useState<any>(false)
    const [edit_risks, setEdit_risks] = useState<any>(false)
    const [delete_risks, setDelete_risks] = useState<any>(false)
    const [editTaskingRisk, setEditTaskingRisk] = useState<any>(false)

    const { toast } = useToast()

    const logBookEntryModel = new LogBookEntryModel()
    const towingChecklistModel = new TowingChecklistModel()
    const riskFactorModel = new RiskFactorModel()
    const crewMemberModel = new CrewMembers_LogBookEntrySectionModel()
    const taskingModel = new EventType_TaskingModel()
    const mitigationStrategyModel = new MitigationStrategyModel()

    const [selectedAuthor, setSelectedAuthor] = useState<any>(null)

    const init_permissions = () => {
        if (permissions) {
            if (hasPermission('EDIT_RISK', permissions)) {
                setEdit_risks(true)
            } else {
                setEdit_risks(false)
            }
            if (hasPermission('DELETE_RISK', permissions)) {
                setDelete_risks(true)
            } else {
                setDelete_risks(false)
            }
            if (hasPermission('EDIT_LOGBOOKENTRY_RISK_ANALYSIS', permissions)) {
                setEditTaskingRisk(true)
            } else {
                setEditTaskingRisk(false)
            }
        }
    }

    useEffect(() => {
        setPermissions(getPermissions)
        init_permissions()
    }, [])

    useEffect(() => {
        init_permissions()
    }, [permissions])

    const [
        getSectionCrewMembers_LogBookEntrySection,
        // Unused loading state
        {
            /* loading: crewMembersLoading */
        },
    ] = useLazyQuery(CrewMembers_LogBookEntrySection, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            let data = response.readCrewMembers_LogBookEntrySections.nodes
            const crewMembers = data
                .map((member: any) => {
                    return {
                        label: `${member.crewMember.firstName ?? ''} ${member.crewMember.surname ?? ''}`,
                        value: member.crewMember.id,
                    }
                })
                .filter((member: any) => member.value != logbook.master.id)
            setMembers(uniqBy([...members, ...crewMembers], 'value'))
        },
        onError: (error: any) => {
            console.error('CrewMembers_LogBookEntrySection error', error)
        },
    })

    const handleSetLogbook = async (logbook: any) => {
        setLogbook(logbook)
        const master = {
            label: `${logbook.master.firstName ?? ''} ${logbook.master.surname ?? ''}`,
            value: logbook.master.id,
        }
        if (+master.value > 0) {
            if (Array.isArray(members)) {
                setMembers(uniqBy([...members, master], 'value'))
            } else {
                setMembers([master])
            }
        }
        const sections = logbook.logBookEntrySections.nodes.filter(
            (node: any) => {
                return (
                    node.className ===
                    'SeaLogs\\CrewMembers_LogBookEntrySection'
                )
            },
        )
        if (sections) {
            const sectionIDs = sections.map((section: any) => section.id)
            if (sectionIDs?.length > 0) {
                if (offline) {
                    const data = await crewMemberModel.getByIds(sectionIDs)
                    const crewMembers = data.map((member: any) => {
                        return {
                            label: `${member.crewMember.firstName ?? ''} ${member.crewMember.surname ?? ''}`,
                            value: member.crewMember.id,
                        }
                    })
                    if (Array.isArray(members)) {
                        setMembers(
                            uniqBy([...members, ...crewMembers], 'value'),
                        )
                    } else {
                        setMembers(crewMembers)
                    }
                } else {
                    getSectionCrewMembers_LogBookEntrySection({
                        variables: {
                            filter: { id: { in: sectionIDs } },
                        },
                    })
                }
            }
        }
    }

    if (logentryID > 0 && !offline) {
        getLogBookEntryByID(+logentryID, handleSetLogbook)
    }

    useEffect(() => {
        if (crewMembers) {
            const members = crewMembers.map((member: any) => {
                return {
                    label: `${member.crewMember.firstName ?? ''} ${member.crewMember.surname ?? ''}`,
                    value: member.crewMemberID,
                }
            })
            setMembers(members)
        }
    }, [crewMembers])

    const handleTaskingRiskFieldChange =
        (field: string) => async (check: boolean) => {
            if (!editTaskingRisk || !edit_risks) {
                toast({
                    title: 'Permission Error',
                    description:
                        'You do not have permission to edit this section',
                    variant: 'destructive',
                })
                return
            }
            setRiskBuffer({
                ...riskBuffer,
                [field]: check ? 'on' : 'off',
            })
            if (+riskAnalysis?.id > 0) {
                if (offline) {
                    const data = await towingChecklistModel.save({
                        id: riskAnalysis.id,
                        [field]: check ? true : false,
                    })
                    const towingChecklistData =
                        await towingChecklistModel.getById(data.id)
                    setRiskAnalysis(towingChecklistData)
                } else {
                    updateTowingChecklist({
                        variables: {
                            input: {
                                id: riskAnalysis.id,
                                [field]: check ? true : false,
                            },
                        },
                    })
                }
            }
        }

    const [updateTowingChecklist] = useMutation(UpdateTowingChecklist, {
        onCompleted: (data) => {
            getRiskAnalysis({
                variables: {
                    id: data.updateTowingChecklist.id,
                },
            })
        },
        onError: (error) => {
            console.error('onError', error)
        },
    })

    const fields = [
        {
            name: 'ConductSAP',
            label: 'Conduct SAP',
            value: 'conductSAP',
            checked: riskBuffer?.conductSAP
                ? riskBuffer.conductSAP === 'on'
                : riskAnalysis?.conductSAP,
            handleChange: handleTaskingRiskFieldChange('conductSAP'),
            description: (
                <small>
                    <div>Conduct SAP prior to approaching the vessel.</div>
                    <div>
                        Check for fittings on the vessel that could damage the
                        CRV when coming alongside.
                    </div>
                </small>
            ),
        },
        {
            name: 'InvestigateNatureOfIssue',
            label: 'Investigate nature of the issue',
            value: 'investigateNatureOfIssue',
            checked: riskBuffer?.investigateNatureOfIssue
                ? riskBuffer.investigateNatureOfIssue === 'on'
                : riskAnalysis?.investigateNatureOfIssue,
            handleChange: handleTaskingRiskFieldChange(
                'investigateNatureOfIssue',
            ),
            description: (
                <small>
                    <div>
                        Ascertain the nature of the problem, any damage, or
                        taking on water.
                    </div>
                    <div>
                        Does a crew member need to go on board the other vessel
                        to assist?
                    </div>
                </small>
            ),
        },
        {
            name: 'EveryoneOnBoardOk',
            label: 'Everyone on board ok?',
            value: 'everyoneOnBoardOk',
            checked: riskBuffer?.everyoneOnBoardOk
                ? riskBuffer.everyoneOnBoardOk === 'on'
                : riskAnalysis?.everyoneOnBoardOk,
            handleChange: handleTaskingRiskFieldChange('everyoneOnBoardOk'),
            description: (
                <small>
                    <div>
                        Check how many people are aboard, ensure everyone is
                        accounted for.
                    </div>
                    <div>
                        Check for injuries or medical assistance required.
                    </div>
                </small>
            ),
        },
        {
            name: 'RudderToMidshipsAndTrimmed',
            label: 'Rudder to midships and trimmed appropriately',
            value: 'rudderToMidshipsAndTrimmed',
            checked: riskBuffer?.rudderToMidshipsAndTrimmed
                ? riskBuffer.rudderToMidshipsAndTrimmed === 'on'
                : riskAnalysis?.rudderToMidshipsAndTrimmed,
            handleChange: handleTaskingRiskFieldChange(
                'rudderToMidshipsAndTrimmed',
            ),
            description: (
                <small>
                    <div>
                        Check steering isn’t impaired in any way and have the
                        rudder secured amidships or have the vessel steer for
                        the stern of CRV.
                    </div>
                    <div>Check the vessel is optimally trimmed for towing.</div>
                </small>
            ),
        },
        {
            name: 'LifejacketsOn',
            label: 'Lifejackets on',
            value: 'lifejacketsOn',
            checked: riskBuffer?.lifejacketsOn
                ? riskBuffer.lifejacketsOn === 'on'
                : riskAnalysis?.lifejacketsOn,
            handleChange: handleTaskingRiskFieldChange('lifejacketsOn'),
            description: (
                <small>
                    <div>Request that everyone wears a lifejacket.</div>
                </small>
            ),
        },
        {
            name: 'CommunicationsEstablished',
            label: 'Communications Established',
            value: 'communicationsEstablished',
            checked: riskBuffer?.communicationsEstablished
                ? riskBuffer.communicationsEstablished === 'on'
                : riskAnalysis?.communicationsEstablished,
            handleChange: handleTaskingRiskFieldChange(
                'communicationsEstablished',
            ),
            description: (
                <small>
                    <div>
                        Ensure that communications have been established and
                        checked prior to beginning the tow, i.e., VHF, hand
                        signals, and/or light signals if the tow is to be
                        conducted at night.
                    </div>
                    <div>
                        Ensure there is agreement on where to tow the vessel to.
                    </div>
                </small>
            ),
        },
        {
            name: 'SecureAndSafeTowing',
            label: 'Secure and safe towing',
            value: 'secureAndSafeTowing',
            checked: riskBuffer?.secureAndSafeTowing
                ? riskBuffer.secureAndSafeTowing === 'on'
                : riskAnalysis?.secureAndSafeTowing,
            handleChange: handleTaskingRiskFieldChange('secureAndSafeTowing'),
            description: (
                <small>
                    <div>Towline securely attached</div>
                    <div>Ensure everything on board is stowed and secure.</div>
                    <div>
                        Confirm waterline length/cruising speed of the vessel
                        (safe tow speed).
                    </div>
                    <div>Confirm attachment points for the towline.</div>
                    <div>Confirm that the towline is securely attached.</div>
                    <div>
                        Ensure that no one on the other vessel is in close
                        proximity to the towline before commencing the tow.
                    </div>
                    <div>
                        Turn on CRV towing lights and other vessel’s navigation
                        lights.
                    </div>
                    <div>
                        Post towline lookout with responsibility for quick
                        release of the tow / must carry or have a knife handy.
                    </div>
                </small>
            ),
        },
    ]

    const createOfflineTowingChecklist = async () => {
        const data = await towingChecklistModel.save({ id: generateUniqueId() })
        setTowingChecklistID(+data.id)
        await taskingModel.save({
            id:
                towingChecklistID > 0
                    ? towingChecklistID
                    : selectedEvent?.eventType_Tasking?.id,
            towingChecklistID: +data.id,
        })
        const towingChecklistData = await towingChecklistModel.getById(data.id)
        setRiskAnalysis(towingChecklistData)
    }

    const offlineGetRiskAnalysis = async () => {
        const data = await towingChecklistModel.getById(
            towingChecklistID > 0
                ? towingChecklistID
                : selectedEvent?.eventType_Tasking?.towingChecklist?.id,
        )
        setRiskAnalysis(data)
    }

    useEffect(() => {
        if (selectedEvent || towingChecklistID > 0) {
            if (
                selectedEvent?.eventType_Tasking?.towingChecklist?.id > 0 ||
                towingChecklistID > 0
            ) {
                if (offline) {
                    offlineGetRiskAnalysis()
                } else {
                    getRiskAnalysis({
                        variables: {
                            id:
                                towingChecklistID > 0
                                    ? towingChecklistID
                                    : selectedEvent?.eventType_Tasking
                                          ?.towingChecklist?.id,
                        },
                    })
                }
            } else {
                if (offline) {
                    createOfflineTowingChecklist()
                } else {
                    createTowingChecklist({
                        variables: {
                            input: {},
                        },
                    })
                }
            }
        }
    }, [selectedEvent, towingChecklistID])

    const offlineMount = async () => {
        const data = await riskFactorModel.getByFieldID(
            'type',
            'TowingChecklist',
        )
        const risks = Array.from(
            new Set(data.map((risk: any) => risk.title)),
        )?.map((risk: any) => ({ label: risk, value: risk }))
        setAllRisks(risks)
        setAllRiskFactors(data)
    }
    useEffect(() => {
        if (offline) {
            offlineMount()
        } else {
            getRiskFactors({
                variables: {
                    filter: { type: { eq: 'TowingChecklist' } },
                },
            })
        }
    }, [])

    const [getRiskFactors] = useLazyQuery(GetRiskFactors, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (data) => {
            const risks = Array.from(
                new Set(
                    data.readRiskFactors.nodes?.map((risk: any) => risk.title),
                ),
            )?.map((risk: any) => ({ label: risk, value: risk }))
            setAllRisks(risks)
            setAllRiskFactors(data.readRiskFactors.nodes)
        },
        onError: (error) => {
            console.error('onError', error)
        },
    })

    const [getRiskAnalysis] = useLazyQuery(TowingChecklist, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (data) => {
            setRiskAnalysis(data.readOneTowingChecklist)
        },
        onError: (error) => {
            console.error('onError', error)
        },
    })

    const [createTowingChecklist] = useMutation(CreateTowingChecklist, {
        onCompleted: (data) => {
            setTowingChecklistID(+data.createTowingChecklist.id)
            updateEvent({
                variables: {
                    input: {
                        id:
                            towingChecklistID > 0
                                ? towingChecklistID
                                : selectedEvent?.eventType_Tasking?.id,
                        towingChecklistID: +data.createTowingChecklist.id,
                    },
                },
            })
            getRiskAnalysis({
                variables: {
                    id: data.createTowingChecklist.id,
                },
            })
        },
        onError: (error) => {
            console.error('onError', error)
        },
    })

    const [updateEvent] = useMutation(UpdateEventType_Tasking, {
        onCompleted: () => {},
        onError: (error) => {
            console.error('onError', error)
        },
    })

    const updateRiskAnalysisMember = async (memberID: number) => {
        if (!editTaskingRisk || !edit_risks) {
            toast({
                title: 'Permission Error',
                description: 'You do not have permission to edit this section',
                variant: 'destructive',
            })
            return
        }
        if (offline) {
            const data = await towingChecklistModel.save({
                id: riskAnalysis.id,
                memberID: memberID,
            })
            const towingChecklistData = await towingChecklistModel.getById(
                data.id,
            )
            setRiskAnalysis(towingChecklistData)
        } else {
            updateTowingChecklist({
                variables: {
                    input: {
                        id: riskAnalysis.id,
                        memberID: memberID,
                    },
                },
            })
        }
    }

    const handleEditorChange = (newContent: any) => {
        setContent(newContent)
    }

    const riskImpacts = [
        { value: 'Low', label: 'Low impact' },
        { value: 'Medium', label: 'Medium impact' },
        { value: 'High', label: 'High impact' },
        { value: 'Severe', label: 'Severe impact' },
    ]
    const handleSaveRisk = async () => {
        if (currentRisk.id > 0) {
            if (offline) {
                await riskFactorModel.save({
                    id: currentRisk.id,
                    type: 'TowingChecklist',
                    title: currentRisk.title,
                    impact: currentRisk?.impact ? currentRisk?.impact : 'Low',
                    probability: currentRisk?.probability
                        ? currentRisk?.probability
                        : 5,
                    mitigationStrategy:
                        currentStrategies.length > 0
                            ? currentStrategies.map((s: any) => s.id).join(',')
                            : '',
                    towingChecklistID: riskAnalysis?.id,
                })
                setOpenRiskDialog(false)
                const data = await riskFactorModel.getByFieldID(
                    'type',
                    'TowingChecklist',
                )
                const risks = Array.from(
                    new Set(data.map((risk: any) => risk.title)),
                )?.map((risk: any) => ({ label: risk, value: risk }))
                setAllRisks(risks)
                setAllRiskFactors(data)
                const towingChecklistData = await towingChecklistModel.getById(
                    towingChecklistID > 0
                        ? towingChecklistID
                        : selectedEvent?.eventType_Tasking?.towingChecklist?.id,
                )
                setRiskAnalysis(towingChecklistData)
            } else {
                updateRiskFactor({
                    variables: {
                        input: {
                            id: currentRisk.id,
                            type: 'TowingChecklist',
                            title: currentRisk.title,
                            impact: currentRisk?.impact
                                ? currentRisk?.impact
                                : 'Low',
                            probability: currentRisk?.probability
                                ? currentRisk?.probability
                                : 5,
                            mitigationStrategy:
                                currentStrategies.length > 0
                                    ? currentStrategies
                                          .map((s: any) => s.id)
                                          .join(',')
                                    : '',
                            towingChecklistID: riskAnalysis?.id,
                        },
                    },
                })
            }
        } else {
            if (offline) {
                await riskFactorModel.save({
                    id: generateUniqueId(),
                    type: 'TowingChecklist',
                    title: currentRisk.title,
                    impact: currentRisk?.impact ? currentRisk?.impact : 'Low',
                    probability: currentRisk?.probability
                        ? currentRisk?.probability
                        : 5,
                    mitigationStrategy:
                        currentStrategies.length > 0
                            ? currentStrategies.map((s: any) => s.id).join(',')
                            : '',
                    towingChecklistID: riskAnalysis?.id,
                    vesselID: vesselID,
                })
                setOpenRiskDialog(false)
                const data = await riskFactorModel.getByFieldID(
                    'type',
                    'TowingChecklist',
                )
                const risks = Array.from(
                    new Set(data.map((risk: any) => risk.title)),
                )?.map((risk: any) => ({ label: risk, value: risk }))
                setAllRisks(risks)
                setAllRiskFactors(data)
                const towingChecklistData = await towingChecklistModel.getById(
                    towingChecklistID > 0
                        ? towingChecklistID
                        : selectedEvent?.eventType_Tasking?.towingChecklist?.id,
                )
                setRiskAnalysis(towingChecklistData)
            } else {
                createRiskFactor({
                    variables: {
                        input: {
                            type: 'TowingChecklist',
                            title: currentRisk.title,
                            impact: currentRisk?.impact
                                ? currentRisk?.impact
                                : 'Low',
                            probability: currentRisk?.probability
                                ? currentRisk?.probability
                                : 5,
                            mitigationStrategy:
                                currentStrategies.length > 0
                                    ? currentStrategies
                                          .map((s: any) => s.id)
                                          .join(',')
                                    : '',
                            towingChecklistID: riskAnalysis?.id,
                            vesselID: vesselID,
                        },
                    },
                })
            }
        }
    }

    const [createMitigationStrategy] = useMutation(CreateMitigationStrategy, {
        onCompleted: (data) => {
            setCurrentStrategies([
                ...currentStrategies,
                { id: data.createMitigationStrategy.id, strategy: content },
            ])
            setContent('')
        },
        onError: (error) => {
            console.error('onError', error)
        },
    })

    const [createRiskFactor] = useMutation(CreateRiskFactor, {
        onCompleted: () => {
            setOpenRiskDialog(false)
            getRiskFactors({
                variables: {
                    filter: { type: { eq: 'TowingChecklist' } },
                },
            })
            getRiskAnalysis({
                variables: {
                    id:
                        towingChecklistID > 0
                            ? towingChecklistID
                            : selectedEvent?.eventType_Tasking?.towingChecklist
                                  ?.id,
                },
            })
        },
        onError: (error) => {
            console.error('onError', error)
        },
    })

    const [updateRiskFactor] = useMutation(UpdateRiskFactor, {
        onCompleted: () => {
            setOpenRiskDialog(false)
            getRiskFactors({
                variables: {
                    filter: { type: { eq: 'TowingChecklist' } },
                },
            })
            getRiskAnalysis({
                variables: {
                    id:
                        towingChecklistID > 0
                            ? towingChecklistID
                            : selectedEvent?.eventType_Tasking?.towingChecklist
                                  ?.id,
                },
            })
        },
        onError: (error) => {
            console.error('onError', error)
        },
    })

    const handleRiskValue = (v: any) => {
        setCurrentRisk({
            ...currentRisk,
            title: v?.value,
        })
        setRiskValue({ value: v.value, label: v.value })
        if (
            allRiskFactors?.filter(
                (risk: any) =>
                    risk.title === v.value &&
                    risk.mitigationStrategy.nodes?.length > 0,
            ).length > 0
        ) {
            setRecommendedStratagies(
                Array.from(
                    new Set(
                        allRiskFactors
                            ?.filter(
                                (r: any) =>
                                    r.title === v.value &&
                                    r.mitigationStrategy.nodes?.length > 0,
                            )
                            .map((r: any) => r.mitigationStrategy.nodes)[0]
                            .map((s: any) => ({
                                id: s.id,
                                strategy: s.strategy,
                            })),
                    ),
                ),
            )
        } else {
            setRecommendedStratagies(false)
        }
    }

    // This function is not used directly in the component but is kept for reference
    // and potential future use
    // const handleCreateRisk = (inputValue: any) => {
    //     setCurrentRisk({
    //         ...currentRisk,
    //         title: inputValue,
    //     })
    //     setRiskValue({ value: inputValue, label: inputValue })
    //     if (allRisks) {
    //         const risk = [...allRisks, { value: inputValue, label: inputValue }]
    //         setAllRisks(risk)
    //     } else {
    //         setAllRisks([{ value: inputValue, label: inputValue }])
    //     }
    // }

    const handleDeleteRisk = async (riskToDelete: any) => {
        if (offline) {
            await riskFactorModel.save({
                id: riskToDelete.id,
                towingChecklistID: 0,
                vesselID: 0,
            })
            setOpenRiskDialog(false)
            const data = await riskFactorModel.getByFieldID(
                'type',
                'TowingChecklist',
            )
            const risks = Array.from(
                new Set(data.map((risk: any) => risk.title)),
            )?.map((risk: any) => ({ label: risk, value: risk }))
            setAllRisks(risks)
            setAllRiskFactors(data)
            const towingChecklistData = await towingChecklistModel.getById(
                towingChecklistID > 0
                    ? towingChecklistID
                    : selectedEvent?.eventType_Tasking?.towingChecklist?.id,
            )
            setRiskAnalysis(towingChecklistData)
        } else {
            updateRiskFactor({
                variables: {
                    input: {
                        id: riskToDelete.id,
                        towingChecklistID: 0,
                        vesselID: 0,
                    },
                },
            })
        }
    }

    const handleSetCurrentStrategies = (strategy: any) => {
        if (currentStrategies.length > 0) {
            if (currentStrategies.find((s: any) => s.id === strategy.id)) {
                setCurrentStrategies(
                    currentStrategies.filter((s: any) => s.id !== strategy.id),
                )
            } else {
                setCurrentStrategies([...currentStrategies, strategy])
            }
        } else {
            setCurrentStrategies([strategy])
        }
    }

    const handleNewStrategy = async () => {
        if (content) {
            if (offline) {
                const data = await mitigationStrategyModel.save({
                    id: generateUniqueId(),
                    strategy: content,
                })
                const newStrategies = [
                    ...currentStrategies,
                    { id: data.id, strategy: content },
                ]
                setCurrentRisk({
                    ...currentRisk,
                    mitigationStrategy: { nodes: newStrategies },
                })
                setCurrentStrategies(newStrategies)
                setContent('')
            } else {
                createMitigationStrategy({
                    variables: {
                        input: { strategy: content },
                    },
                })
            }
        }
        setOpenRecommendedstrategy(false)
    }

    const handleSetRiskValue = (v: any) => {
        setRiskValue({
            value: v.title,
            label: v.title,
        })
        if (v.mitigationStrategy.nodes) {
            setCurrentStrategies(v.mitigationStrategy.nodes)
        }
        if (
            allRiskFactors?.filter(
                (risk: any) =>
                    risk.title === v.title &&
                    risk.mitigationStrategy.nodes?.length > 0,
            ).length > 0
        ) {
            setRecommendedStratagies(
                Array.from(
                    new Set(
                        allRiskFactors
                            ?.filter(
                                (r: any) =>
                                    r.title === v.title &&
                                    r.mitigationStrategy.nodes?.length > 0,
                            )
                            .map((r: any) => r.mitigationStrategy.nodes)[0]
                            .map((s: any) => ({
                                id: s.id,
                                strategy: s.strategy,
                            })),
                    ),
                ),
            )
        } else {
            setRecommendedStratagies(false)
        }
    }

    const offlineGetLogBookEntryByID = async () => {
        const logbook = await logBookEntryModel.getById(logentryID)
        handleSetLogbook(logbook)
    }
    useEffect(() => {
        if (offline) {
            offlineGetLogBookEntryByID()
        }
    }, [offline])
    useEffect(() => {
        if (members && riskAnalysis) {
            const member = members.find(
                (member: any) => member.value == riskAnalysis.member.id,
            )
            setSelectedAuthor(member)
        }
    }, [members, riskAnalysis])

    useEffect(() => {
        setAllChecked(fields.every((field) => field.checked))
    }, [fields])

    return (
        <div>
            <RiskAnalysisSheet
                open={open}
                onOpenChange={(isOpen) => {
                    if (onOpenChange) {
                        onOpenChange(isOpen)
                    }
                }}
                onSidebarClose={() => {
                    onSidebarClose()
                    if (onOpenChange) {
                        onOpenChange(false)
                    }
                }}
                title="Risk Analysis"
                subtitle="Towing"
                checkFields={fields}
                riskFactors={riskAnalysis?.riskFactors?.nodes || []}
                crewMembers={
                    members
                        ? members.map((m: any) => ({
                              ...m,
                              value: String(m.value),
                          }))
                        : []
                }
                selectedAuthor={selectedAuthor}
                onAuthorChange={(value: any) => {
                    setSelectedAuthor(value)
                    if (value) {
                        updateRiskAnalysisMember(value.value)
                    }
                }}
                canEdit={editTaskingRisk && edit_risks}
                canDeleteRisks={editTaskingRisk && delete_risks}
                onRiskClick={(risk: any) => {
                    if (!editTaskingRisk || !edit_risks) {
                        toast({
                            title: 'Permission Error',
                            description:
                                'You do not have permission to edit this section',
                            variant: 'destructive',
                        })
                        return
                    }
                    handleSetRiskValue(risk)
                    setCurrentRisk(risk)
                    setOpenRiskDialog(true)
                }}
                onAddRiskClick={() => {
                    if (!editTaskingRisk || !edit_risks) {
                        toast({
                            title: 'Permission Error',
                            description:
                                'You do not have permission to edit this section',
                            variant: 'destructive',
                        })
                        return
                    }
                    setCurrentRisk({})
                    setContent('')
                    setRiskValue(null)
                    setOpenRiskDialog(true)
                }}
                onRiskDelete={(risk: any) => {
                    handleDeleteRisk(risk)
                }}
                setAllChecked={setAllChecked}
            />

            <RiskDialog
                open={openRiskDialog}
                onOpenChange={setOpenRiskDialog}
                onSave={handleSaveRisk}
                currentRisk={currentRisk}
                riskOptions={allRisks || []}
                riskValue={riskValue}
                onRiskValueChange={handleRiskValue}
                riskImpacts={riskImpacts}
                onRiskImpactChange={(value: any) =>
                    setCurrentRisk({
                        ...currentRisk,
                        impact: value?.value,
                    })
                }
                onRiskProbabilityChange={(value: number) =>
                    setCurrentRisk({
                        ...currentRisk,
                        probability: value,
                    })
                }
                currentStrategies={currentStrategies}
                content={content}
                onAddStrategyClick={() => setOpenRecommendedstrategy(true)}
            />
            <StrategyDialog
                open={openRecommendedstrategy}
                onOpenChange={setOpenRecommendedstrategy}
                onSave={handleNewStrategy}
                currentRisk={currentRisk}
                recommendedStrategies={recommendedStratagies}
                currentStrategies={currentStrategies}
                onStrategySelect={(strategy: any) => {
                    setRecommendedstrategy(strategy)
                    handleSetCurrentStrategies(strategy)
                    setCurrentRisk({
                        ...currentRisk,
                        mitigationStrategy: strategy,
                    })
                    setUpdateStrategy(false)
                }}
                content={content}
                onEditorChange={handleEditorChange}
            />
        </div>
    )
}
