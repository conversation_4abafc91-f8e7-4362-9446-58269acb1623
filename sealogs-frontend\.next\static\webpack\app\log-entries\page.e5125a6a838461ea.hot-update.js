"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/forms/tasking.tsx":
/*!**********************************************!*\
  !*** ./src/app/ui/logbook/forms/tasking.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Tasking; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/alert-dialog-new */ \"(app-pages-browser)/./src/components/ui/alert-dialog-new.tsx\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var lodash_trim__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash/trim */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/trim.js\");\n/* harmony import */ var lodash_trim__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(lodash_trim__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_location__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../components/location */ \"(app-pages-browser)/./src/app/ui/logbook/components/location.tsx\");\n/* harmony import */ var _vessel_rescue_fields__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./vessel-rescue-fields */ \"(app-pages-browser)/./src/app/ui/logbook/forms/vessel-rescue-fields.tsx\");\n/* harmony import */ var _person_rescue_field__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./person-rescue-field */ \"(app-pages-browser)/./src/app/ui/logbook/forms/person-rescue-field.tsx\");\n/* harmony import */ var _editor__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../../editor */ \"(app-pages-browser)/./src/app/ui/editor.tsx\");\n/* harmony import */ var _components_time__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../components/time */ \"(app-pages-browser)/./src/app/ui/logbook/components/time.tsx\");\n/* harmony import */ var _risk_analysis__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./risk-analysis */ \"(app-pages-browser)/./src/app/ui/logbook/forms/risk-analysis.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Check_SquareArrowOutUpRight_X_lucide_react__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Check,SquareArrowOutUpRight,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Check_SquareArrowOutUpRight_X_lucide_react__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Check,SquareArrowOutUpRight,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/square-arrow-out-up-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Check_SquareArrowOutUpRight_X_lucide_react__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Check,SquareArrowOutUpRight,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Check_SquareArrowOutUpRight_X_lucide_react__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Check,SquareArrowOutUpRight,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_lib_icons_SealogsFuelIcon__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/app/lib/icons/SealogsFuelIcon */ \"(app-pages-browser)/./src/app/lib/icons/SealogsFuelIcon.ts\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_offline_models_fuelTank__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/app/offline/models/fuelTank */ \"(app-pages-browser)/./src/app/offline/models/fuelTank.js\");\n/* harmony import */ var _app_offline_models_tripEvent__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/app/offline/models/tripEvent */ \"(app-pages-browser)/./src/app/offline/models/tripEvent.js\");\n/* harmony import */ var _app_offline_models_eventType_Tasking__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/app/offline/models/eventType_Tasking */ \"(app-pages-browser)/./src/app/offline/models/eventType_Tasking.js\");\n/* harmony import */ var _app_offline_models_geoLocation__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/app/offline/models/geoLocation */ \"(app-pages-browser)/./src/app/offline/models/geoLocation.js\");\n/* harmony import */ var _app_offline_models_fuelLog__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/app/offline/models/fuelLog */ \"(app-pages-browser)/./src/app/offline/models/fuelLog.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Combobox is already imported from '@/components/ui/comboBox'\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Tasking(param) {\n    let { geoLocations, currentTrip = false, updateTripReport, selectedEvent = false, tripReport, closeModal, type, logBookConfig, previousDropEvent, vessel, members, locked, offline = false, fuelLogs } = param;\n    var _currentTrip_tripEvents_nodes_find_eventType_Tasking, _currentTrip_tripEvents_nodes_find, _currentTrip_tripEvents, _currentTrip_tripEvents_nodes_find_eventType_Tasking1, _currentTrip_tripEvents_nodes_find1, _currentTrip_tripEvents1, _currentTrip_tripEvents_nodes_find2, _currentTrip_tripEvents2, _currentTrip_tripEvents_nodes_find3, _currentTrip_tripEvents3, _currentTrip_tripEvents_nodes_find4, _currentTrip_tripEvents4, _currentTrip_tripEvents_nodes_find5, _currentTrip_tripEvents5;\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_15__.useSearchParams)();\n    var _searchParams_get;\n    const logentryID = (_searchParams_get = searchParams.get(\"logentryID\")) !== null && _searchParams_get !== void 0 ? _searchParams_get : 0;\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    const [locations, setLocations] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [time, setTime] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(dayjs__WEBPACK_IMPORTED_MODULE_2___default()().format(\"HH:mm\"));\n    const [tasking, setTasking] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [currentEvent, setCurrentEvent] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(selectedEvent);\n    const [parentLocation, setParentLocation] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [openRiskAnalysis, setOpenRiskAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [pauseGroup, setPauseGroup] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [openTaskID, setOpenTaskID] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [completedTaskID, setCompletedTaskID] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [towingChecklistID, setTowingChecklistID] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(0);\n    const [groupID, setGroupID] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [tripEvent, setTripEvent] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [currentIncident, setCurrentIncident] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [taskingPausedValue, setTaskingPausedValue] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [taskingResumedValue, setTaskingResumedValue] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [taskingCompleteValue, setTaskingCompleteValue] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [locationDescription, setLocationDescription] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [allChecked, setAllChecked] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    // const [members, setMembers] = useState<any>(false)\n    const [logbook, setLogbook] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [fuelTankList, setFuelTankList] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [fuelTankBuffer, setFuelTankBuffer] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [updatedFuelLogs, setUpdatedFuelLogs] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [location, setLocation] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        latitude: \"\",\n        longitude: \"\"\n    });\n    const [currentLocation, setCurrentLocation] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        latitude: \"\",\n        longitude: \"\"\n    });\n    const [openNewLocationDialog, setOpenNewLocationDialog] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [editTaskingRisk, setEditTaskingRisk] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const fuelTankModel = new _app_offline_models_fuelTank__WEBPACK_IMPORTED_MODULE_18__[\"default\"]();\n    const tripEventModel = new _app_offline_models_tripEvent__WEBPACK_IMPORTED_MODULE_19__[\"default\"]();\n    const taskingModel = new _app_offline_models_eventType_Tasking__WEBPACK_IMPORTED_MODULE_20__[\"default\"]();\n    const geoLocationModel = new _app_offline_models_geoLocation__WEBPACK_IMPORTED_MODULE_21__[\"default\"]();\n    const fuelLogModel = new _app_offline_models_fuelLog__WEBPACK_IMPORTED_MODULE_22__[\"default\"]();\n    const init_permissions = ()=>{\n        if (permissions) {\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_17__.hasPermission)(\"EDIT_LOGBOOKENTRY_RISK_ANALYSIS\", permissions)) {\n                setEditTaskingRisk(true);\n            } else {\n                setEditTaskingRisk(false);\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_17__.getPermissions);\n        init_permissions();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        init_permissions();\n    }, [\n        permissions\n    ]);\n    const [queryGetFuelTanks] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_31__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_7__.GET_FUELTANKS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readFuelTanks.nodes;\n            // Initialize currentLevel for each tank if not already set\n            const initializedData = data.map((tank)=>{\n                var _tank_currentLevel;\n                return {\n                    ...tank,\n                    currentLevel: (_tank_currentLevel = tank.currentLevel) !== null && _tank_currentLevel !== void 0 ? _tank_currentLevel : getInitialFuelLevel(tank)\n                };\n            });\n            setFuelTankList(initializedData);\n        },\n        onError: (error)=>{\n            console.error(\"getFuelTanks error\", error);\n        }\n    });\n    const getFuelTanks = async (fuelTankIds)=>{\n        if (offline) {\n            const data = await fuelTankModel.getByIds(fuelTankIds);\n            // Initialize currentLevel for each tank if not already set\n            const initializedData = data.map((tank)=>{\n                var _tank_currentLevel;\n                return {\n                    ...tank,\n                    currentLevel: (_tank_currentLevel = tank.currentLevel) !== null && _tank_currentLevel !== void 0 ? _tank_currentLevel : getInitialFuelLevel(tank)\n                };\n            });\n            setFuelTankList(initializedData);\n        } else {\n            await queryGetFuelTanks({\n                variables: {\n                    id: fuelTankIds\n                }\n            });\n        }\n    };\n    const handleSetVessel = (vessel)=>{\n        var _vessel_parentComponent_Components;\n        const fuelTankIds = vessel === null || vessel === void 0 ? void 0 : (_vessel_parentComponent_Components = vessel.parentComponent_Components) === null || _vessel_parentComponent_Components === void 0 ? void 0 : _vessel_parentComponent_Components.nodes.filter((item)=>item.basicComponent.componentCategory === \"FuelTank\").map((item)=>{\n            return item.basicComponent.id;\n        });\n        fuelTankIds.length > 0 && getFuelTanks(fuelTankIds);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (vessel) {\n            handleSetVessel(vessel);\n        }\n    }, [\n        vessel\n    ]);\n    const handleTimeChange = (date)=>{\n        setTime(dayjs__WEBPACK_IMPORTED_MODULE_2___default()(date).format(\"HH:mm\"));\n    };\n    const offlineGetPreviousDropEvent = async ()=>{\n        const event = await tripEventModel.getById(previousDropEvent === null || previousDropEvent === void 0 ? void 0 : previousDropEvent.id);\n        if (event) {\n            var _event_eventType_Tasking, _event_eventType_Tasking1, _event_eventType_Tasking2;\n            setGroupID((_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.groupID);\n            if (((_event_eventType_Tasking1 = event.eventType_Tasking) === null || _event_eventType_Tasking1 === void 0 ? void 0 : _event_eventType_Tasking1.lat) && ((_event_eventType_Tasking2 = event.eventType_Tasking) === null || _event_eventType_Tasking2 === void 0 ? void 0 : _event_eventType_Tasking2.long)) {\n                var _event_eventType_Tasking3, _event_eventType_Tasking4;\n                setCurrentLocation({\n                    latitude: (_event_eventType_Tasking3 = event.eventType_Tasking) === null || _event_eventType_Tasking3 === void 0 ? void 0 : _event_eventType_Tasking3.lat,\n                    longitude: (_event_eventType_Tasking4 = event.eventType_Tasking) === null || _event_eventType_Tasking4 === void 0 ? void 0 : _event_eventType_Tasking4.long\n                });\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (selectedEvent) {\n            setCurrentEvent(selectedEvent);\n            getCurrentEvent(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id);\n        } else {\n            if ((previousDropEvent === null || previousDropEvent === void 0 ? void 0 : previousDropEvent.id) > 0) {\n                if (offline) {\n                    offlineGetPreviousDropEvent();\n                } else {\n                    getPreviousDropEvent({\n                        variables: {\n                            id: previousDropEvent === null || previousDropEvent === void 0 ? void 0 : previousDropEvent.id\n                        }\n                    });\n                }\n            }\n        }\n    }, [\n        selectedEvent\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (currentEvent) {\n            getCurrentEvent(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n        }\n    }, [\n        currentEvent\n    ]);\n    const handleTaskingPauseChange = (selectedTask)=>{\n        setPauseGroup(selectedTask.value);\n        setTaskingPausedValue(selectedTask);\n    };\n    const [getPreviousDropEvent] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_31__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_7__.GetTripEvent, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const event = response.readOneTripEvent;\n            if (event) {\n                var _event_eventType_Tasking, _event_eventType_Tasking1, _event_eventType_Tasking2;\n                setGroupID((_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.groupID);\n                if (((_event_eventType_Tasking1 = event.eventType_Tasking) === null || _event_eventType_Tasking1 === void 0 ? void 0 : _event_eventType_Tasking1.lat) && ((_event_eventType_Tasking2 = event.eventType_Tasking) === null || _event_eventType_Tasking2 === void 0 ? void 0 : _event_eventType_Tasking2.long)) {\n                    var _event_eventType_Tasking3, _event_eventType_Tasking4;\n                    setCurrentLocation({\n                        latitude: (_event_eventType_Tasking3 = event.eventType_Tasking) === null || _event_eventType_Tasking3 === void 0 ? void 0 : _event_eventType_Tasking3.lat,\n                        longitude: (_event_eventType_Tasking4 = event.eventType_Tasking) === null || _event_eventType_Tasking4 === void 0 ? void 0 : _event_eventType_Tasking4.long\n                    });\n                }\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error getting previous event\", error);\n        }\n    });\n    const getCurrentEvent = async (id)=>{\n        if (offline) {\n            var _currentTrip_tripEvents;\n            const event = await tripEventModel.getById(id);\n            if (event) {\n                var _event_eventType_Tasking, _event_eventType_Tasking1, _event_eventType_Tasking2, _event_eventType_Tasking3, _event_eventType_Tasking4, _event_eventType_Tasking5, _event_eventType_Tasking6, _event_eventType_Tasking7, _event_eventType_Tasking8, _event_eventType_Tasking_operationType, _event_eventType_Tasking9, _event_eventType_Tasking10, _event_eventType_Tasking11, _event_eventType_Tasking12, _event_eventType_Tasking13, _event_eventType_Tasking14, _event_eventType_Tasking15, _event_eventType_Tasking16, _event_eventType_Tasking17, _event_eventType_Tasking18, _event_eventType_Tasking19, _event_eventType_Tasking20, _event_eventType_Tasking21, _event_eventType_Tasking22, _event_eventType_Tasking23, _event_eventType_Tasking24, _event_eventType_Tasking25, _event_eventType_Tasking26, _event_eventType_Tasking27, _event_eventType_Tasking28, _event_eventType_Tasking29, _event_eventType_Tasking30, _event_eventType_Tasking31, _event_eventType_Tasking32, _event_eventType_Tasking33, _event_eventType_Tasking34, _event_eventType_Tasking_fuelLog, _event_eventType_Tasking35, _event_eventType_Tasking36, _event_eventType_Tasking37, _event_eventType_Tasking38, _event_eventType_Tasking39, _event_eventType_Tasking40, _event_eventType_Tasking41, _event_eventType_Tasking42, _event_eventType_Tasking43, _event_eventType_Tasking44, _event_eventType_Tasking45;\n                // eventType_TaskingID\n                if (!event.eventType_Tasking) {\n                    const eventType_Tasking = await taskingModel.getById(event.eventType_TaskingID);\n                    event.eventType_Tasking = eventType_Tasking;\n                }\n                setTripEvent(event);\n                setTasking({\n                    geoLocationID: ((_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.geoLocationID) ? (_event_eventType_Tasking1 = event.eventType_Tasking) === null || _event_eventType_Tasking1 === void 0 ? void 0 : _event_eventType_Tasking1.geoLocationID : null,\n                    time: (_event_eventType_Tasking2 = event.eventType_Tasking) === null || _event_eventType_Tasking2 === void 0 ? void 0 : _event_eventType_Tasking2.time,\n                    title: ((_event_eventType_Tasking3 = event.eventType_Tasking) === null || _event_eventType_Tasking3 === void 0 ? void 0 : _event_eventType_Tasking3.title) ? (_event_eventType_Tasking4 = event.eventType_Tasking) === null || _event_eventType_Tasking4 === void 0 ? void 0 : _event_eventType_Tasking4.title : \"\",\n                    fuelLevel: ((_event_eventType_Tasking5 = event.eventType_Tasking) === null || _event_eventType_Tasking5 === void 0 ? void 0 : _event_eventType_Tasking5.fuelLevel) ? (_event_eventType_Tasking6 = event.eventType_Tasking) === null || _event_eventType_Tasking6 === void 0 ? void 0 : _event_eventType_Tasking6.fuelLevel : \"\",\n                    type: ((_event_eventType_Tasking7 = event.eventType_Tasking) === null || _event_eventType_Tasking7 === void 0 ? void 0 : _event_eventType_Tasking7.type) ? (_event_eventType_Tasking8 = event.eventType_Tasking) === null || _event_eventType_Tasking8 === void 0 ? void 0 : _event_eventType_Tasking8.type : \"\",\n                    operationType: (_event_eventType_Tasking9 = event.eventType_Tasking) === null || _event_eventType_Tasking9 === void 0 ? void 0 : (_event_eventType_Tasking_operationType = _event_eventType_Tasking9.operationType) === null || _event_eventType_Tasking_operationType === void 0 ? void 0 : _event_eventType_Tasking_operationType.replaceAll(\"_\", \" \"),\n                    lat: ((_event_eventType_Tasking10 = event.eventType_Tasking) === null || _event_eventType_Tasking10 === void 0 ? void 0 : _event_eventType_Tasking10.lat) ? (_event_eventType_Tasking11 = event.eventType_Tasking) === null || _event_eventType_Tasking11 === void 0 ? void 0 : _event_eventType_Tasking11.lat : \"\",\n                    long: ((_event_eventType_Tasking12 = event.eventType_Tasking) === null || _event_eventType_Tasking12 === void 0 ? void 0 : _event_eventType_Tasking12.long) ? (_event_eventType_Tasking13 = event.eventType_Tasking) === null || _event_eventType_Tasking13 === void 0 ? void 0 : _event_eventType_Tasking13.long : \"\",\n                    vesselRescueID: ((_event_eventType_Tasking14 = event.eventType_Tasking) === null || _event_eventType_Tasking14 === void 0 ? void 0 : _event_eventType_Tasking14.vesselRescueID) ? (_event_eventType_Tasking15 = event.eventType_Tasking) === null || _event_eventType_Tasking15 === void 0 ? void 0 : _event_eventType_Tasking15.vesselRescueID : 0,\n                    personRescueID: ((_event_eventType_Tasking16 = event.eventType_Tasking) === null || _event_eventType_Tasking16 === void 0 ? void 0 : _event_eventType_Tasking16.personRescueID) ? (_event_eventType_Tasking17 = event.eventType_Tasking) === null || _event_eventType_Tasking17 === void 0 ? void 0 : _event_eventType_Tasking17.personRescueID : 0,\n                    groupID: ((_event_eventType_Tasking18 = event.eventType_Tasking) === null || _event_eventType_Tasking18 === void 0 ? void 0 : _event_eventType_Tasking18.groupID) ? (_event_eventType_Tasking19 = event.eventType_Tasking) === null || _event_eventType_Tasking19 === void 0 ? void 0 : _event_eventType_Tasking19.groupID : null,\n                    comments: ((_event_eventType_Tasking20 = event.eventType_Tasking) === null || _event_eventType_Tasking20 === void 0 ? void 0 : _event_eventType_Tasking20.comments) ? (_event_eventType_Tasking21 = event.eventType_Tasking) === null || _event_eventType_Tasking21 === void 0 ? void 0 : _event_eventType_Tasking21.comments : \"\",\n                    tripEventID: ((_event_eventType_Tasking22 = event.eventType_Tasking) === null || _event_eventType_Tasking22 === void 0 ? void 0 : _event_eventType_Tasking22.id) ? (_event_eventType_Tasking23 = event.eventType_Tasking) === null || _event_eventType_Tasking23 === void 0 ? void 0 : _event_eventType_Tasking23.id : null,\n                    pausedTaskID: ((_event_eventType_Tasking24 = event.eventType_Tasking) === null || _event_eventType_Tasking24 === void 0 ? void 0 : _event_eventType_Tasking24.pausedTaskID) ? (_event_eventType_Tasking25 = event.eventType_Tasking) === null || _event_eventType_Tasking25 === void 0 ? void 0 : _event_eventType_Tasking25.pausedTaskID : null,\n                    openTaskID: ((_event_eventType_Tasking26 = event.eventType_Tasking) === null || _event_eventType_Tasking26 === void 0 ? void 0 : _event_eventType_Tasking26.openTaskID) ? (_event_eventType_Tasking27 = event.eventType_Tasking) === null || _event_eventType_Tasking27 === void 0 ? void 0 : _event_eventType_Tasking27.openTaskID : null,\n                    completedTaskID: ((_event_eventType_Tasking28 = event.eventType_Tasking) === null || _event_eventType_Tasking28 === void 0 ? void 0 : _event_eventType_Tasking28.completedTaskID) ? (_event_eventType_Tasking29 = event.eventType_Tasking) === null || _event_eventType_Tasking29 === void 0 ? void 0 : _event_eventType_Tasking29.completedTaskID : null,\n                    status: (_event_eventType_Tasking30 = event.eventType_Tasking) === null || _event_eventType_Tasking30 === void 0 ? void 0 : _event_eventType_Tasking30.status,\n                    cgop: ((_event_eventType_Tasking31 = event.eventType_Tasking) === null || _event_eventType_Tasking31 === void 0 ? void 0 : _event_eventType_Tasking31.cgop) ? (_event_eventType_Tasking32 = event.eventType_Tasking) === null || _event_eventType_Tasking32 === void 0 ? void 0 : _event_eventType_Tasking32.cgop : \"\",\n                    sarop: ((_event_eventType_Tasking33 = event.eventType_Tasking) === null || _event_eventType_Tasking33 === void 0 ? void 0 : _event_eventType_Tasking33.sarop) ? (_event_eventType_Tasking34 = event.eventType_Tasking) === null || _event_eventType_Tasking34 === void 0 ? void 0 : _event_eventType_Tasking34.sarop : \"\",\n                    fuelLog: (_event_eventType_Tasking35 = event.eventType_Tasking) === null || _event_eventType_Tasking35 === void 0 ? void 0 : (_event_eventType_Tasking_fuelLog = _event_eventType_Tasking35.fuelLog) === null || _event_eventType_Tasking_fuelLog === void 0 ? void 0 : _event_eventType_Tasking_fuelLog.nodes\n                });\n                setGroupID(event === null || event === void 0 ? void 0 : (_event_eventType_Tasking36 = event.eventType_Tasking) === null || _event_eventType_Tasking36 === void 0 ? void 0 : _event_eventType_Tasking36.groupID);\n                setContent(event === null || event === void 0 ? void 0 : (_event_eventType_Tasking37 = event.eventType_Tasking) === null || _event_eventType_Tasking37 === void 0 ? void 0 : _event_eventType_Tasking37.comments);\n                setTime((_event_eventType_Tasking38 = event.eventType_Tasking) === null || _event_eventType_Tasking38 === void 0 ? void 0 : _event_eventType_Tasking38.time);\n                setCompletedTaskID(((_event_eventType_Tasking39 = event.eventType_Tasking) === null || _event_eventType_Tasking39 === void 0 ? void 0 : _event_eventType_Tasking39.completedTaskID) ? event.eventType_Tasking.completedTaskID : null);\n                setOpenTaskID(((_event_eventType_Tasking40 = event.eventType_Tasking) === null || _event_eventType_Tasking40 === void 0 ? void 0 : _event_eventType_Tasking40.openTaskID) ? (_event_eventType_Tasking41 = event.eventType_Tasking) === null || _event_eventType_Tasking41 === void 0 ? void 0 : _event_eventType_Tasking41.openTaskID : null);\n                setPauseGroup(((_event_eventType_Tasking42 = event.eventType_Tasking) === null || _event_eventType_Tasking42 === void 0 ? void 0 : _event_eventType_Tasking42.pausedTaskID) ? (_event_eventType_Tasking43 = event.eventType_Tasking) === null || _event_eventType_Tasking43 === void 0 ? void 0 : _event_eventType_Tasking43.pausedTaskID : null);\n                if (((_event_eventType_Tasking44 = event.eventType_Tasking) === null || _event_eventType_Tasking44 === void 0 ? void 0 : _event_eventType_Tasking44.lat) && ((_event_eventType_Tasking45 = event.eventType_Tasking) === null || _event_eventType_Tasking45 === void 0 ? void 0 : _event_eventType_Tasking45.long)) {\n                    var _event_eventType_Tasking46, _event_eventType_Tasking47;\n                    setCurrentLocation({\n                        latitude: (_event_eventType_Tasking46 = event.eventType_Tasking) === null || _event_eventType_Tasking46 === void 0 ? void 0 : _event_eventType_Tasking46.lat,\n                        longitude: (_event_eventType_Tasking47 = event.eventType_Tasking) === null || _event_eventType_Tasking47 === void 0 ? void 0 : _event_eventType_Tasking47.long\n                    });\n                }\n            }\n            const resumedEvent = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : _currentTrip_tripEvents.nodes.filter((event)=>{\n                var _event_eventType_Tasking;\n                return (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" && (event === null || event === void 0 ? void 0 : event.id) !== (currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id) && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingResumed\";\n            });\n            if (resumedEvent) {\n                var _resumedEvent__eventType_Tasking, _resumedEvent_;\n                setGroupID((_resumedEvent_ = resumedEvent[0]) === null || _resumedEvent_ === void 0 ? void 0 : (_resumedEvent__eventType_Tasking = _resumedEvent_.eventType_Tasking) === null || _resumedEvent__eventType_Tasking === void 0 ? void 0 : _resumedEvent__eventType_Tasking.groupID);\n            }\n        } else {\n            getTripEvent({\n                variables: {\n                    id: id\n                }\n            });\n        }\n    };\n    const [getTripEvent] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_31__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_7__.GetTripEvent, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            var _currentTrip_tripEvents;\n            const event = response.readOneTripEvent;\n            if (event) {\n                var _event_eventType_Tasking, _event_eventType_Tasking1, _event_eventType_Tasking2, _event_eventType_Tasking3, _event_eventType_Tasking4, _event_eventType_Tasking5, _event_eventType_Tasking6, _event_eventType_Tasking7, _event_eventType_Tasking8, _event_eventType_Tasking_operationType, _event_eventType_Tasking9, _event_eventType_Tasking10, _event_eventType_Tasking11, _event_eventType_Tasking12, _event_eventType_Tasking13, _event_eventType_Tasking14, _event_eventType_Tasking15, _event_eventType_Tasking16, _event_eventType_Tasking17, _event_eventType_Tasking18, _event_eventType_Tasking19, _event_eventType_Tasking20, _event_eventType_Tasking21, _event_eventType_Tasking22, _event_eventType_Tasking23, _event_eventType_Tasking24, _event_eventType_Tasking25, _event_eventType_Tasking26, _event_eventType_Tasking27, _event_eventType_Tasking28, _event_eventType_Tasking29, _event_eventType_Tasking30, _event_eventType_Tasking31, _event_eventType_Tasking32, _event_eventType_Tasking33, _event_eventType_Tasking34, _event_eventType_Tasking_fuelLog, _event_eventType_Tasking35, _event_eventType_Tasking36, _event_eventType_Tasking37, _event_eventType_Tasking38, _event_eventType_Tasking39, _event_eventType_Tasking40, _event_eventType_Tasking41, _event_eventType_Tasking42, _event_eventType_Tasking43, _event_eventType_Tasking44, _event_eventType_Tasking45;\n                setTripEvent(event);\n                setTasking({\n                    geoLocationID: ((_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.geoLocationID) ? (_event_eventType_Tasking1 = event.eventType_Tasking) === null || _event_eventType_Tasking1 === void 0 ? void 0 : _event_eventType_Tasking1.geoLocationID : null,\n                    time: (_event_eventType_Tasking2 = event.eventType_Tasking) === null || _event_eventType_Tasking2 === void 0 ? void 0 : _event_eventType_Tasking2.time,\n                    title: ((_event_eventType_Tasking3 = event.eventType_Tasking) === null || _event_eventType_Tasking3 === void 0 ? void 0 : _event_eventType_Tasking3.title) ? (_event_eventType_Tasking4 = event.eventType_Tasking) === null || _event_eventType_Tasking4 === void 0 ? void 0 : _event_eventType_Tasking4.title : \"\",\n                    fuelLevel: ((_event_eventType_Tasking5 = event.eventType_Tasking) === null || _event_eventType_Tasking5 === void 0 ? void 0 : _event_eventType_Tasking5.fuelLevel) ? (_event_eventType_Tasking6 = event.eventType_Tasking) === null || _event_eventType_Tasking6 === void 0 ? void 0 : _event_eventType_Tasking6.fuelLevel : \"\",\n                    type: ((_event_eventType_Tasking7 = event.eventType_Tasking) === null || _event_eventType_Tasking7 === void 0 ? void 0 : _event_eventType_Tasking7.type) ? (_event_eventType_Tasking8 = event.eventType_Tasking) === null || _event_eventType_Tasking8 === void 0 ? void 0 : _event_eventType_Tasking8.type : \"\",\n                    operationType: (_event_eventType_Tasking9 = event.eventType_Tasking) === null || _event_eventType_Tasking9 === void 0 ? void 0 : (_event_eventType_Tasking_operationType = _event_eventType_Tasking9.operationType) === null || _event_eventType_Tasking_operationType === void 0 ? void 0 : _event_eventType_Tasking_operationType.replaceAll(\"_\", \" \"),\n                    lat: ((_event_eventType_Tasking10 = event.eventType_Tasking) === null || _event_eventType_Tasking10 === void 0 ? void 0 : _event_eventType_Tasking10.lat) ? (_event_eventType_Tasking11 = event.eventType_Tasking) === null || _event_eventType_Tasking11 === void 0 ? void 0 : _event_eventType_Tasking11.lat : \"\",\n                    long: ((_event_eventType_Tasking12 = event.eventType_Tasking) === null || _event_eventType_Tasking12 === void 0 ? void 0 : _event_eventType_Tasking12.long) ? (_event_eventType_Tasking13 = event.eventType_Tasking) === null || _event_eventType_Tasking13 === void 0 ? void 0 : _event_eventType_Tasking13.long : \"\",\n                    vesselRescueID: ((_event_eventType_Tasking14 = event.eventType_Tasking) === null || _event_eventType_Tasking14 === void 0 ? void 0 : _event_eventType_Tasking14.vesselRescueID) ? (_event_eventType_Tasking15 = event.eventType_Tasking) === null || _event_eventType_Tasking15 === void 0 ? void 0 : _event_eventType_Tasking15.vesselRescueID : 0,\n                    personRescueID: ((_event_eventType_Tasking16 = event.eventType_Tasking) === null || _event_eventType_Tasking16 === void 0 ? void 0 : _event_eventType_Tasking16.personRescueID) ? (_event_eventType_Tasking17 = event.eventType_Tasking) === null || _event_eventType_Tasking17 === void 0 ? void 0 : _event_eventType_Tasking17.personRescueID : 0,\n                    groupID: ((_event_eventType_Tasking18 = event.eventType_Tasking) === null || _event_eventType_Tasking18 === void 0 ? void 0 : _event_eventType_Tasking18.groupID) ? (_event_eventType_Tasking19 = event.eventType_Tasking) === null || _event_eventType_Tasking19 === void 0 ? void 0 : _event_eventType_Tasking19.groupID : null,\n                    comments: ((_event_eventType_Tasking20 = event.eventType_Tasking) === null || _event_eventType_Tasking20 === void 0 ? void 0 : _event_eventType_Tasking20.comments) ? (_event_eventType_Tasking21 = event.eventType_Tasking) === null || _event_eventType_Tasking21 === void 0 ? void 0 : _event_eventType_Tasking21.comments : \"\",\n                    tripEventID: ((_event_eventType_Tasking22 = event.eventType_Tasking) === null || _event_eventType_Tasking22 === void 0 ? void 0 : _event_eventType_Tasking22.id) ? (_event_eventType_Tasking23 = event.eventType_Tasking) === null || _event_eventType_Tasking23 === void 0 ? void 0 : _event_eventType_Tasking23.id : null,\n                    pausedTaskID: ((_event_eventType_Tasking24 = event.eventType_Tasking) === null || _event_eventType_Tasking24 === void 0 ? void 0 : _event_eventType_Tasking24.pausedTaskID) ? (_event_eventType_Tasking25 = event.eventType_Tasking) === null || _event_eventType_Tasking25 === void 0 ? void 0 : _event_eventType_Tasking25.pausedTaskID : null,\n                    openTaskID: ((_event_eventType_Tasking26 = event.eventType_Tasking) === null || _event_eventType_Tasking26 === void 0 ? void 0 : _event_eventType_Tasking26.openTaskID) ? (_event_eventType_Tasking27 = event.eventType_Tasking) === null || _event_eventType_Tasking27 === void 0 ? void 0 : _event_eventType_Tasking27.openTaskID : null,\n                    completedTaskID: ((_event_eventType_Tasking28 = event.eventType_Tasking) === null || _event_eventType_Tasking28 === void 0 ? void 0 : _event_eventType_Tasking28.completedTaskID) ? (_event_eventType_Tasking29 = event.eventType_Tasking) === null || _event_eventType_Tasking29 === void 0 ? void 0 : _event_eventType_Tasking29.completedTaskID : null,\n                    status: (_event_eventType_Tasking30 = event.eventType_Tasking) === null || _event_eventType_Tasking30 === void 0 ? void 0 : _event_eventType_Tasking30.status,\n                    cgop: ((_event_eventType_Tasking31 = event.eventType_Tasking) === null || _event_eventType_Tasking31 === void 0 ? void 0 : _event_eventType_Tasking31.cgop) ? (_event_eventType_Tasking32 = event.eventType_Tasking) === null || _event_eventType_Tasking32 === void 0 ? void 0 : _event_eventType_Tasking32.cgop : \"\",\n                    sarop: ((_event_eventType_Tasking33 = event.eventType_Tasking) === null || _event_eventType_Tasking33 === void 0 ? void 0 : _event_eventType_Tasking33.sarop) ? (_event_eventType_Tasking34 = event.eventType_Tasking) === null || _event_eventType_Tasking34 === void 0 ? void 0 : _event_eventType_Tasking34.sarop : \"\",\n                    fuelLog: (_event_eventType_Tasking35 = event.eventType_Tasking) === null || _event_eventType_Tasking35 === void 0 ? void 0 : (_event_eventType_Tasking_fuelLog = _event_eventType_Tasking35.fuelLog) === null || _event_eventType_Tasking_fuelLog === void 0 ? void 0 : _event_eventType_Tasking_fuelLog.nodes\n                });\n                setGroupID(event === null || event === void 0 ? void 0 : (_event_eventType_Tasking36 = event.eventType_Tasking) === null || _event_eventType_Tasking36 === void 0 ? void 0 : _event_eventType_Tasking36.groupID);\n                setContent(event === null || event === void 0 ? void 0 : (_event_eventType_Tasking37 = event.eventType_Tasking) === null || _event_eventType_Tasking37 === void 0 ? void 0 : _event_eventType_Tasking37.comments);\n                setTime((_event_eventType_Tasking38 = event.eventType_Tasking) === null || _event_eventType_Tasking38 === void 0 ? void 0 : _event_eventType_Tasking38.time);\n                setCompletedTaskID(((_event_eventType_Tasking39 = event.eventType_Tasking) === null || _event_eventType_Tasking39 === void 0 ? void 0 : _event_eventType_Tasking39.completedTaskID) ? event.eventType_Tasking.completedTaskID : null);\n                setOpenTaskID(((_event_eventType_Tasking40 = event.eventType_Tasking) === null || _event_eventType_Tasking40 === void 0 ? void 0 : _event_eventType_Tasking40.openTaskID) ? (_event_eventType_Tasking41 = event.eventType_Tasking) === null || _event_eventType_Tasking41 === void 0 ? void 0 : _event_eventType_Tasking41.openTaskID : null);\n                setPauseGroup(((_event_eventType_Tasking42 = event.eventType_Tasking) === null || _event_eventType_Tasking42 === void 0 ? void 0 : _event_eventType_Tasking42.pausedTaskID) ? (_event_eventType_Tasking43 = event.eventType_Tasking) === null || _event_eventType_Tasking43 === void 0 ? void 0 : _event_eventType_Tasking43.pausedTaskID : null);\n                if (((_event_eventType_Tasking44 = event.eventType_Tasking) === null || _event_eventType_Tasking44 === void 0 ? void 0 : _event_eventType_Tasking44.lat) && ((_event_eventType_Tasking45 = event.eventType_Tasking) === null || _event_eventType_Tasking45 === void 0 ? void 0 : _event_eventType_Tasking45.long)) {\n                    var _event_eventType_Tasking46, _event_eventType_Tasking47;\n                    setCurrentLocation({\n                        latitude: (_event_eventType_Tasking46 = event.eventType_Tasking) === null || _event_eventType_Tasking46 === void 0 ? void 0 : _event_eventType_Tasking46.lat,\n                        longitude: (_event_eventType_Tasking47 = event.eventType_Tasking) === null || _event_eventType_Tasking47 === void 0 ? void 0 : _event_eventType_Tasking47.long\n                    });\n                }\n            }\n            const resumedEvent = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : _currentTrip_tripEvents.nodes.filter((event)=>{\n                var _event_eventType_Tasking;\n                return (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" && (event === null || event === void 0 ? void 0 : event.id) !== (currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id) && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingResumed\";\n            });\n            if (resumedEvent) {\n                var _resumedEvent__eventType_Tasking, _resumedEvent_;\n                setGroupID((_resumedEvent_ = resumedEvent[0]) === null || _resumedEvent_ === void 0 ? void 0 : (_resumedEvent__eventType_Tasking = _resumedEvent_.eventType_Tasking) === null || _resumedEvent__eventType_Tasking === void 0 ? void 0 : _resumedEvent__eventType_Tasking.groupID);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error getting current event\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (geoLocations) {\n            setLocations([\n                {\n                    label: \"--- Add new location ---\",\n                    value: \"newLocation\"\n                },\n                ...geoLocations.filter((location)=>location.title).map((location)=>({\n                        label: location.title,\n                        value: location.id,\n                        latitude: location.lat,\n                        longitude: location.long\n                    }))\n            ]);\n        }\n    }, [\n        geoLocations\n    ]);\n    const handleSave = async function() {\n        let vesselRescueID = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, personRescueID = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        const variables = {\n            input: {\n                geoLocationID: tasking === null || tasking === void 0 ? void 0 : tasking.geoLocationID,\n                time: time,\n                title: tasking === null || tasking === void 0 ? void 0 : tasking.title,\n                fuelLevel: tasking === null || tasking === void 0 ? void 0 : tasking.fuelLevel,\n                type: type,\n                operationType: tasking === null || tasking === void 0 ? void 0 : tasking.operationType,\n                lat: currentLocation.latitude.toString(),\n                long: currentLocation.longitude.toString(),\n                vesselRescueID: vesselRescueID > 0 ? vesselRescueID : tasking === null || tasking === void 0 ? void 0 : tasking.vesselRescueID,\n                personRescueID: personRescueID > 0 ? personRescueID : tasking === null || tasking === void 0 ? void 0 : tasking.personRescueID,\n                currentEntryID: currentTrip.id,\n                tripEventID: tasking === null || tasking === void 0 ? void 0 : tasking.id,\n                pausedTaskID: +pauseGroup,\n                openTaskID: +openTaskID,\n                completedTaskID: +completedTaskID,\n                comments: content,\n                groupID: +groupID,\n                status: \"Open\",\n                cgop: (tasking === null || tasking === void 0 ? void 0 : tasking.cgop) ? tasking.cgop : null,\n                sarop: (tasking === null || tasking === void 0 ? void 0 : tasking.sarop) ? tasking.sarop : null\n            }\n        };\n        if (pauseGroup > 0) {\n            if (offline) {\n                const x = await taskingModel.save({\n                    id: +pauseGroup,\n                    status: \"Paused\",\n                    tripEventID: currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id\n                });\n                updateTripReport(currentTrip);\n                updateTripReport({\n                    id: tripReport.map((trip)=>trip.id)\n                });\n                closeModal();\n            } else {\n                updateEventType_tasking({\n                    variables: {\n                        input: {\n                            id: +pauseGroup,\n                            status: \"Paused\",\n                            tripEventID: currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id\n                        }\n                    }\n                });\n            }\n        }\n        if (openTaskID > 0) {\n            if (offline) {\n                const x = await taskingModel.save({\n                    id: +openTaskID,\n                    status: \"Open\",\n                    tripEventID: currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id\n                });\n                updateTripReport(currentTrip);\n                updateTripReport({\n                    id: tripReport.map((trip)=>trip.id)\n                });\n                closeModal();\n            } else {\n                updateEventType_tasking({\n                    variables: {\n                        input: {\n                            id: +openTaskID,\n                            status: \"Open\",\n                            tripEventID: currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id\n                        }\n                    }\n                });\n            }\n        }\n        if (completedTaskID > 0 && !currentEvent) {\n            if (offline) {\n                const x = await taskingModel.save({\n                    id: +completedTaskID > 0 ? +completedTaskID : (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_23__.generateUniqueId)(),\n                    status: \"Completed\",\n                    tripEventID: currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id\n                });\n                updateTripReport(currentTrip);\n                updateTripReport({\n                    id: tripReport.map((trip)=>trip.id)\n                });\n                closeModal();\n            } else {\n                updateEventType_tasking({\n                    variables: {\n                        input: {\n                            id: +completedTaskID,\n                            status: \"Completed\",\n                            tripEventID: currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id\n                        }\n                    }\n                });\n            }\n        }\n        if (currentEvent) {\n            if (offline) {\n                const data = await taskingModel.save({\n                    ...variables.input,\n                    id: +(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.eventType_TaskingID),\n                    tripEventID: +(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id)\n                });\n                updateTripReport(currentTrip);\n                updateTripReport({\n                    id: tripReport.map((trip)=>trip.id)\n                });\n                closeModal();\n                updateFuelLogs(+(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.eventType_TaskingID));\n                await getCurrentEvent(+(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.eventType_TaskingID));\n            } else {\n                updateEventType_tasking({\n                    variables: {\n                        input: {\n                            id: +(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.eventType_TaskingID),\n                            ...variables.input\n                        }\n                    }\n                });\n                updateFuelLogs(+(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.eventType_TaskingID));\n            }\n        } else {\n            if (offline) {\n                const newID = (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_23__.generateUniqueId)();\n                await tripEventModel.save({\n                    id: newID,\n                    eventCategory: \"Tasking\",\n                    eventType_TaskingID: +newID,\n                    logBookEntrySectionID: currentTrip.id\n                });\n                const data = await taskingModel.save({\n                    id: +newID,\n                    geoLocationID: tasking === null || tasking === void 0 ? void 0 : tasking.geoLocationID,\n                    time: time,\n                    title: tasking === null || tasking === void 0 ? void 0 : tasking.title,\n                    fuelLevel: tasking === null || tasking === void 0 ? void 0 : tasking.fuelLevel,\n                    type: type,\n                    operationType: tasking === null || tasking === void 0 ? void 0 : tasking.operationType,\n                    lat: currentLocation.latitude.toString(),\n                    long: currentLocation.longitude.toString(),\n                    vesselRescueID: vesselRescueID,\n                    personRescueID: personRescueID,\n                    currentEntryID: currentTrip.id,\n                    pausedTaskID: +pauseGroup,\n                    openTaskID: +openTaskID,\n                    completedTaskID: +completedTaskID,\n                    comments: content,\n                    groupID: +groupID,\n                    status: \"Open\",\n                    cgop: (tasking === null || tasking === void 0 ? void 0 : tasking.cgop) ? tasking.cgop : getPreviousCGOP(false),\n                    sarop: (tasking === null || tasking === void 0 ? void 0 : tasking.sarop) ? tasking.sarop : getPreviousSAROP(false),\n                    tripEventID: newID\n                });\n                updateFuelLogs(+data.id);\n                updateTripReport(currentTrip);\n                updateTripReport({\n                    id: tripReport.map((trip)=>trip.id)\n                });\n            } else {\n                createEventType_Tasking({\n                    variables: {\n                        input: {\n                            geoLocationID: tasking === null || tasking === void 0 ? void 0 : tasking.geoLocationID,\n                            time: time,\n                            title: tasking === null || tasking === void 0 ? void 0 : tasking.title,\n                            fuelLevel: tasking === null || tasking === void 0 ? void 0 : tasking.fuelLevel,\n                            type: type,\n                            operationType: tasking === null || tasking === void 0 ? void 0 : tasking.operationType,\n                            lat: currentLocation.latitude.toString(),\n                            long: currentLocation.longitude.toString(),\n                            vesselRescueID: vesselRescueID,\n                            personRescueID: personRescueID,\n                            currentEntryID: currentTrip.id,\n                            pausedTaskID: +pauseGroup,\n                            openTaskID: +openTaskID,\n                            completedTaskID: +completedTaskID,\n                            comments: content,\n                            groupID: +groupID,\n                            status: \"Open\",\n                            cgop: (tasking === null || tasking === void 0 ? void 0 : tasking.cgop) ? tasking.cgop : getPreviousCGOP(false),\n                            sarop: (tasking === null || tasking === void 0 ? void 0 : tasking.sarop) ? tasking.sarop : getPreviousSAROP(false)\n                        }\n                    }\n                });\n            }\n        }\n        setCompletedTaskID(false);\n        setOpenTaskID(false);\n        setPauseGroup(false);\n    };\n    const [createEventType_Tasking] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_32__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_6__.CreateEventType_Tasking, {\n        onCompleted: (response)=>{\n            const data = response.createEventType_Tasking;\n            updateFuelLogs(+data.id);\n            updateTripReport(currentTrip);\n            updateTripReport({\n                id: tripReport.map((trip)=>trip.id)\n            });\n            closeModal();\n        },\n        onError: (error)=>{\n            toast({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: error.message\n            });\n        }\n    });\n    const [updateEventType_tasking] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_32__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_6__.UpdateEventType_Tasking, {\n        onCompleted: (response)=>{\n            const data = response.updateEventType_tasking;\n            updateTripReport(currentTrip);\n            updateTripReport({\n                id: tripReport.map((trip)=>trip.id)\n            });\n            closeModal();\n        },\n        onError: (error)=>{\n            console.error(\"Error updating activity type tasking\", error);\n        }\n    });\n    const handleOperationTypeChange = (selectedOperation)=>{\n        if (selectedOperation.value === \"newLocation\") {\n            toast({\n                title: \"Getting your current location...\",\n                description: \"Please wait...\"\n            });\n            if (\"geolocation\" in navigator) {\n                navigator.geolocation.getCurrentPosition((param)=>{\n                    let { coords } = param;\n                    const { latitude, longitude } = coords;\n                    setLocation({\n                        latitude,\n                        longitude\n                    });\n                    toast({\n                        title: \"Success\",\n                        description: \"Location found\"\n                    });\n                    setOpenNewLocationDialog(true);\n                });\n            } else {\n                toast({\n                    variant: \"destructive\",\n                    title: \"Error\",\n                    description: \"Geolocation is not supported by your browser\"\n                });\n                setOpenNewLocationDialog(true);\n            }\n        } else {\n            setTasking({\n                ...tasking,\n                operationType: selectedOperation.value\n            });\n        }\n    };\n    const handleLocationChange = (value)=>{\n        // If value is null or undefined, clear the location\n        if (!value) {\n            setTasking({\n                ...tasking,\n                geoLocationID: 0,\n                lat: null,\n                long: null\n            });\n            return;\n        }\n        // Handle \"Add new location\" option\n        if (value.value === \"newLocation\") {\n            toast({\n                title: \"Getting your current location...\",\n                description: \"Please wait...\"\n            });\n            if (\"geolocation\" in navigator) {\n                navigator.geolocation.getCurrentPosition((param)=>{\n                    let { coords } = param;\n                    const { latitude, longitude } = coords;\n                    setLocation({\n                        latitude,\n                        longitude\n                    });\n                    setOpenNewLocationDialog(true);\n                });\n            } else {\n                toast({\n                    variant: \"destructive\",\n                    title: \"Error\",\n                    description: \"Geolocation is not supported by your browser\"\n                });\n                setOpenNewLocationDialog(true);\n            }\n            return;\n        }\n        // Check if the value is from dropdown selection (has 'value' property)\n        if (value.value) {\n            // Handle location selected from dropdown\n            setTasking({\n                ...tasking,\n                geoLocationID: +value.value,\n                lat: null,\n                long: null\n            });\n            // If the value object has latitude and longitude, update currentLocation\n            if (value.latitude !== undefined && value.longitude !== undefined) {\n                setCurrentLocation({\n                    latitude: value.latitude,\n                    longitude: value.longitude\n                });\n            }\n        } else if (value.latitude !== undefined && value.longitude !== undefined) {\n            // Handle direct coordinates input\n            setTasking({\n                ...tasking,\n                geoLocationID: 0,\n                lat: value.latitude,\n                long: value.longitude\n            });\n            // Update currentLocation\n            setCurrentLocation({\n                latitude: value.latitude,\n                longitude: value.longitude\n            });\n        }\n    };\n    const handleCreateNewLocation = async ()=>{\n        const title = document.getElementById(\"new-location-title\");\n        const latitude = document.getElementById(\"new-location-latitude\");\n        const longitude = document.getElementById(\"new-location-longitude\");\n        if (title && latitude && longitude) {\n            if (offline) {\n                const data = await geoLocationModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_23__.generateUniqueId)(),\n                    title: title.value,\n                    lat: +latitude.value,\n                    long: +longitude.value,\n                    parentID: parentLocation\n                });\n                setLocations([\n                    ...locations,\n                    {\n                        label: data.title,\n                        value: data.id,\n                        latitude: data.lat,\n                        longitude: data.long\n                    }\n                ]);\n                setTasking({\n                    ...tasking,\n                    geoLocationID: data.id\n                });\n                setOpenNewLocationDialog(false);\n            } else {\n                createGeoLocation({\n                    variables: {\n                        input: {\n                            title: title.value,\n                            lat: +latitude.value,\n                            long: +longitude.value,\n                            parentID: parentLocation\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const [createGeoLocation] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_32__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_6__.CREATE_GEO_LOCATION, {\n        onCompleted: (response)=>{\n            const data = response.createGeoLocation;\n            setLocations([\n                ...locations,\n                {\n                    label: data.title,\n                    value: data.id,\n                    latitude: data.lat,\n                    longitude: data.long\n                }\n            ]);\n            setTasking({\n                ...tasking,\n                geoLocationID: data.id\n            });\n            setOpenNewLocationDialog(false);\n        },\n        onError: (error)=>{\n            toast({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: \"Error creating GeoLocation\"\n            });\n            console.error(\"Error creating GeoLocation: \" + error.message);\n            setOpenNewLocationDialog(false);\n            console.error(\"Error creating new location\", error);\n        }\n    });\n    const handleParentLocationChange = (selectedOption)=>{\n        if (selectedOption) {\n            setParentLocation(selectedOption.value);\n        } else {\n            setParentLocation(null);\n        }\n    };\n    const handleEditorChange = (newContent)=>{\n        setContent(newContent);\n    };\n    const handleTaskingGroupChange = (selectedGroup)=>{\n        setGroupID(selectedGroup.value);\n        setOpenTaskID(selectedGroup.value);\n        setTaskingResumedValue(selectedGroup);\n    };\n    const handleTaskingCompleteChange = (selectedGroup)=>{\n        setCompletedTaskID(selectedGroup.value);\n        setTaskingCompleteValue(selectedGroup);\n    };\n    const operationTypes = [\n        {\n            label: \"Vessel Mechanical / equipment failure\",\n            value: \"Vessel Mechanical or equipment failure\"\n        },\n        {\n            label: \"Vessel adrift\",\n            value: \"Vessel adrift\"\n        },\n        {\n            label: \"Vessel aground\",\n            value: \"Vessel aground\"\n        },\n        {\n            label: \"Capsize\",\n            value: \"Capsize\"\n        },\n        {\n            label: \"Vessel requiring tow\",\n            value: \"Vessel requiring tow\"\n        },\n        {\n            label: \"Flare sighting\",\n            value: \"Flare sighting\"\n        },\n        {\n            label: \"Vessel sinking\",\n            value: \"Vessel sinking\"\n        },\n        {\n            label: \"Collision\",\n            value: \"Collision\"\n        },\n        {\n            label: \"Vessel overdue\",\n            value: \"Vessel overdue\"\n        },\n        {\n            label: \"Vessel - other\",\n            value: \"Vessel - other\"\n        },\n        {\n            label: \"Person in water\",\n            value: \"Person in water\"\n        },\n        {\n            label: \"Person lost / missing\",\n            value: \"Person lost or missing\"\n        },\n        {\n            label: \"Suicide\",\n            value: \"Suicide\"\n        },\n        {\n            label: \"Medical condition\",\n            value: \"Medical condition\"\n        },\n        {\n            label: \"Person - other\",\n            value: \"Person - other\"\n        }\n    ];\n    const goSetTaskingTitle = (event)=>{\n        let title = \"\";\n        if (event && event.eventType_Tasking.type === \"TaskingStartUnderway\") {\n            title = event.eventType_Tasking.title;\n        }\n        if (lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4___default()(lodash_trim__WEBPACK_IMPORTED_MODULE_5___default()(tasking.title)) && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4___default()(lodash_trim__WEBPACK_IMPORTED_MODULE_5___default()(title))) {\n            setTasking({\n                ...tasking,\n                title: title\n            });\n        }\n    };\n    const findPreviousEvent = (selectedEvent)=>{\n        const prevEvent = previousDropEvent === null || previousDropEvent === void 0 ? void 0 : previousDropEvent.filter((event)=>event.eventType_Tasking.status === \"Open\").pop();\n        if (type === \"TaskingOnTow\" || type === \"TaskingOnScene\") {\n            if (selectedEvent) {\n                if (selectedEvent.eventType_Tasking.vesselRescueID > 0) {\n                    const res = previousDropEvent.filter((event)=>event.eventType_Tasking.vesselRescueID === selectedEvent.eventType_Tasking.vesselRescueID).pop();\n                    goSetTaskingTitle(res);\n                    return res;\n                }\n                if (selectedEvent.eventType_Tasking.personRescueID > 0) {\n                    const res = previousDropEvent.filter((event)=>event.eventType_Tasking.personRescueID === selectedEvent.eventType_Tasking.personRescueID).pop();\n                    goSetTaskingTitle(res);\n                    return res;\n                }\n            }\n            goSetTaskingTitle(prevEvent);\n            return prevEvent;\n        }\n        if (type === \"TaskingComplete\") {\n            if (completedTaskID > 0) {\n                var _currentTrip_tripEvents;\n                const res = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : _currentTrip_tripEvents.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == completedTaskID);\n                goSetTaskingTitle(res);\n                return res;\n            }\n            if (tasking.completedTaskID > 0) {\n                var _currentTrip_tripEvents1;\n                const res = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents1 = currentTrip.tripEvents) === null || _currentTrip_tripEvents1 === void 0 ? void 0 : _currentTrip_tripEvents1.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == tasking.completedTaskID);\n                goSetTaskingTitle(res);\n                return res;\n            } else {\n                const res = prevEvent ? prevEvent : selectedEvent;\n                goSetTaskingTitle(res);\n                return res;\n            }\n        }\n        goSetTaskingTitle(selectedEvent);\n        return selectedEvent;\n    };\n    const findPreviousRescueID = (rescueID)=>{\n        const prevEvent = previousDropEvent === null || previousDropEvent === void 0 ? void 0 : previousDropEvent.filter((event)=>event.eventType_Tasking.status === \"Open\").pop();\n        if (type === \"TaskingOnTow\" || type === \"TaskingOnScene\") {\n            return prevEvent ? prevEvent.eventType_Tasking.vesselRescueID : rescueID;\n        }\n        if (type === \"TaskingComplete\") {\n            if (tasking.completedTaskID > 0) {\n                return tasking.vesselRescueID;\n            }\n            if (completedTaskID > 0) {\n                var _currentTrip_tripEvents_nodes_find_eventType_Tasking, _currentTrip_tripEvents_nodes_find, _currentTrip_tripEvents;\n                return +(currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find = _currentTrip_tripEvents.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == completedTaskID)) === null || _currentTrip_tripEvents_nodes_find === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find_eventType_Tasking = _currentTrip_tripEvents_nodes_find.eventType_Tasking) === null || _currentTrip_tripEvents_nodes_find_eventType_Tasking === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find_eventType_Tasking.vesselRescueID);\n            }\n            if (tasking.completedTaskID > 0) {\n                var _currentTrip_tripEvents_nodes_find_eventType_Tasking1, _currentTrip_tripEvents_nodes_find1, _currentTrip_tripEvents1;\n                return +(currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents1 = currentTrip.tripEvents) === null || _currentTrip_tripEvents1 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find1 = _currentTrip_tripEvents1.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == tasking.completedTaskID)) === null || _currentTrip_tripEvents_nodes_find1 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find_eventType_Tasking1 = _currentTrip_tripEvents_nodes_find1.eventType_Tasking) === null || _currentTrip_tripEvents_nodes_find_eventType_Tasking1 === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find_eventType_Tasking1.vesselRescueID);\n            } else {\n                return prevEvent ? prevEvent.eventType_Tasking.vesselRescueID : rescueID;\n            }\n        }\n        return rescueID;\n    };\n    const findPreviousHumanRescueID = (rescueID)=>{\n        const prevEvent = previousDropEvent === null || previousDropEvent === void 0 ? void 0 : previousDropEvent.filter((event)=>event.eventType_Tasking.status === \"Open\").pop();\n        if (type === \"TaskingOnTow\" || type === \"TaskingOnScene\") {\n            return prevEvent ? prevEvent.eventType_Tasking.personRescueID : rescueID;\n        }\n        if (type === \"TaskingComplete\") {\n            if (tasking.completedTaskID > 0) {\n                return tasking.personRescueID;\n            }\n            if (completedTaskID > 0) {\n                var _currentTrip_tripEvents_nodes_find_eventType_Tasking, _currentTrip_tripEvents_nodes_find, _currentTrip_tripEvents;\n                return +(currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find = _currentTrip_tripEvents.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == completedTaskID)) === null || _currentTrip_tripEvents_nodes_find === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find_eventType_Tasking = _currentTrip_tripEvents_nodes_find.eventType_Tasking) === null || _currentTrip_tripEvents_nodes_find_eventType_Tasking === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find_eventType_Tasking.personRescueID);\n            }\n            if (tasking.completedTaskID > 0) {\n                var _currentTrip_tripEvents_nodes_find_eventType_Tasking1, _currentTrip_tripEvents_nodes_find1, _currentTrip_tripEvents1;\n                return +(currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents1 = currentTrip.tripEvents) === null || _currentTrip_tripEvents1 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find1 = _currentTrip_tripEvents1.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == tasking.completedTaskID)) === null || _currentTrip_tripEvents_nodes_find1 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find_eventType_Tasking1 = _currentTrip_tripEvents_nodes_find1.eventType_Tasking) === null || _currentTrip_tripEvents_nodes_find_eventType_Tasking1 === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find_eventType_Tasking1.personRescueID);\n            } else {\n                return prevEvent ? prevEvent.eventType_Tasking.personRescueID : rescueID;\n            }\n        }\n        return rescueID;\n    };\n    const currentOperationTypeLabel = (label)=>{\n        return label ? label : \"-- Select operation type --\";\n    };\n    const currentOperationTypeValue = (value)=>{\n        return value;\n    };\n    const getPreviousSAROP = (sarop)=>{\n        var _e_eventType_Tasking;\n        if (currentIncident === \"cgop\") {\n            return \"\";\n        }\n        if (currentIncident === \"sarop\") {\n            return sarop ? sarop : \" \";\n        }\n        const e = previousDropEvent === null || previousDropEvent === void 0 ? void 0 : previousDropEvent.filter((event)=>event.eventType_Tasking.status === \"Open\").pop();\n        if (type === \"TaskingComplete\") {\n            var _currentTrip_tripEvents;\n            const completedEvent = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : _currentTrip_tripEvents.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == completedTaskID);\n        }\n        return (e === null || e === void 0 ? void 0 : (_e_eventType_Tasking = e.eventType_Tasking) === null || _e_eventType_Tasking === void 0 ? void 0 : _e_eventType_Tasking.sarop) ? e.eventType_Tasking.sarop : sarop ? sarop : \"\";\n    };\n    const getPreviousCGOP = (cgop)=>{\n        var _e_eventType_Tasking;\n        if (currentIncident === \"sarop\") {\n            return \"\";\n        }\n        if (currentIncident === \"cgop\") {\n            return cgop ? cgop : \" \";\n        }\n        const e = previousDropEvent === null || previousDropEvent === void 0 ? void 0 : previousDropEvent.filter((event)=>event.eventType_Tasking.status === \"Open\").pop();\n        if (type === \"TaskingComplete\") {\n            var _currentTrip_tripEvents;\n            const completedEvent = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : _currentTrip_tripEvents.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == completedTaskID);\n        }\n        return (e === null || e === void 0 ? void 0 : (_e_eventType_Tasking = e.eventType_Tasking) === null || _e_eventType_Tasking === void 0 ? void 0 : _e_eventType_Tasking.cgop) ? e.eventType_Tasking.cgop : cgop ? cgop : \"\";\n    };\n    const getIsSAROP = (sarop)=>{\n        if (currentIncident === \"cgop\") {\n            return false;\n        }\n        if (currentIncident === \"sarop\") {\n            return true;\n        }\n    };\n    const getIsCGOP = (cgop)=>{\n        if (currentIncident === \"sarop\") {\n            return false;\n        }\n        if (currentIncident === \"cgop\") {\n            return true;\n        }\n    };\n    const getPreviousFuelLevel = (fuelLevel)=>{\n        var _selectedEvent_eventType_Tasking, _currentTrip_tripEvents;\n        if ((selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking === void 0 ? void 0 : _selectedEvent_eventType_Tasking.fuelLevel) > 0) {\n            var _selectedEvent_eventType_Tasking1;\n            return selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking1 = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking1 === void 0 ? void 0 : _selectedEvent_eventType_Tasking1.fuelLevel;\n        }\n        if (fuelLevel || (tasking === null || tasking === void 0 ? void 0 : tasking.updatedFuelLevel)) {\n            return fuelLevel;\n        }\n        const fuelLevels = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : _currentTrip_tripEvents.nodes.filter((event)=>event.eventType_Tasking.fuelLevel > 0).map((event)=>event.eventType_Tasking.fuelLevel);\n        const minFuelLevel = (fuelLevels === null || fuelLevels === void 0 ? void 0 : fuelLevels.length) ? fuelLevels[fuelLevels.length - 1] : fuelLevel;\n        return (fuelLevels === null || fuelLevels === void 0 ? void 0 : fuelLevels.length) ? minFuelLevel : fuelLevel ? fuelLevel : \"\";\n    };\n    const getPreviousTask = (task)=>{\n        var _prevEvent_eventType_Tasking, _prevEvent_eventType_Tasking1, _prevEvent_eventType_Tasking2;\n        if (task) {\n            return task;\n        }\n        const prevEvent = previousDropEvent === null || previousDropEvent === void 0 ? void 0 : previousDropEvent.filter((event)=>event.eventType_Tasking.status === \"Open\").pop();\n        if (type === \"TaskingComplete\") {\n            var _prevEvent_eventType_Tasking3, _prevEvent_eventType_Tasking4, _prevEvent_eventType_Tasking5, _prevEvent_eventType_Tasking6;\n            setCompletedTaskID(prevEvent === null || prevEvent === void 0 ? void 0 : (_prevEvent_eventType_Tasking3 = prevEvent.eventType_Tasking) === null || _prevEvent_eventType_Tasking3 === void 0 ? void 0 : _prevEvent_eventType_Tasking3.id);\n            setTaskingCompleteValue({\n                label: (prevEvent === null || prevEvent === void 0 ? void 0 : (_prevEvent_eventType_Tasking4 = prevEvent.eventType_Tasking) === null || _prevEvent_eventType_Tasking4 === void 0 ? void 0 : _prevEvent_eventType_Tasking4.time) + \" - \" + (prevEvent === null || prevEvent === void 0 ? void 0 : (_prevEvent_eventType_Tasking5 = prevEvent.eventType_Tasking) === null || _prevEvent_eventType_Tasking5 === void 0 ? void 0 : _prevEvent_eventType_Tasking5.title),\n                value: prevEvent === null || prevEvent === void 0 ? void 0 : (_prevEvent_eventType_Tasking6 = prevEvent.eventType_Tasking) === null || _prevEvent_eventType_Tasking6 === void 0 ? void 0 : _prevEvent_eventType_Tasking6.id\n            });\n        }\n        return prevEvent ? {\n            label: (prevEvent === null || prevEvent === void 0 ? void 0 : (_prevEvent_eventType_Tasking = prevEvent.eventType_Tasking) === null || _prevEvent_eventType_Tasking === void 0 ? void 0 : _prevEvent_eventType_Tasking.time) + \" - \" + (prevEvent === null || prevEvent === void 0 ? void 0 : (_prevEvent_eventType_Tasking1 = prevEvent.eventType_Tasking) === null || _prevEvent_eventType_Tasking1 === void 0 ? void 0 : _prevEvent_eventType_Tasking1.title),\n            value: prevEvent === null || prevEvent === void 0 ? void 0 : (_prevEvent_eventType_Tasking2 = prevEvent.eventType_Tasking) === null || _prevEvent_eventType_Tasking2 === void 0 ? void 0 : _prevEvent_eventType_Tasking2.id\n        } : task;\n    };\n    const isVesselRescue = ()=>{\n        var _currentTrip_tripEvents_nodes_find_eventType_Tasking, _currentTrip_tripEvents_nodes_find, _currentTrip_tripEvents;\n        if (type === \"TaskingComplete\" && tasking.completedTaskID > 0) {\n            var _currentTrip_tripEvents_nodes_find_eventType_Tasking1, _currentTrip_tripEvents_nodes_find1, _currentTrip_tripEvents1;\n            return (currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents1 = currentTrip.tripEvents) === null || _currentTrip_tripEvents1 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find1 = _currentTrip_tripEvents1.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == tasking.completedTaskID)) === null || _currentTrip_tripEvents_nodes_find1 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find_eventType_Tasking1 = _currentTrip_tripEvents_nodes_find1.eventType_Tasking) === null || _currentTrip_tripEvents_nodes_find_eventType_Tasking1 === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find_eventType_Tasking1.vesselRescueID) > 0;\n        }\n        if (type === \"TaskingOnScene\" || type === \"TaskingOnTow\") {\n            var _currentTrip_tripEvents2, _latestEvent_eventType_Tasking;\n            var latestEvent;\n            currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents2 = currentTrip.tripEvents) === null || _currentTrip_tripEvents2 === void 0 ? void 0 : _currentTrip_tripEvents2.nodes.filter((event)=>{\n                if ((event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\") {\n                    latestEvent = event;\n                }\n            });\n            return (latestEvent === null || latestEvent === void 0 ? void 0 : (_latestEvent_eventType_Tasking = latestEvent.eventType_Tasking) === null || _latestEvent_eventType_Tasking === void 0 ? void 0 : _latestEvent_eventType_Tasking.vesselRescueID) > 0;\n        }\n        return (currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find = _currentTrip_tripEvents.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == completedTaskID)) === null || _currentTrip_tripEvents_nodes_find === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find_eventType_Tasking = _currentTrip_tripEvents_nodes_find.eventType_Tasking) === null || _currentTrip_tripEvents_nodes_find_eventType_Tasking === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find_eventType_Tasking.vesselRescueID) > 0;\n    };\n    const isPersonRescue = ()=>{\n        var _currentTrip_tripEvents_nodes_find_eventType_Tasking, _currentTrip_tripEvents_nodes_find, _currentTrip_tripEvents;\n        if (type === \"TaskingComplete\" && tasking.completedTaskID > 0) {\n            var _currentTrip_tripEvents_nodes_find_eventType_Tasking1, _currentTrip_tripEvents_nodes_find1, _currentTrip_tripEvents1;\n            return (currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents1 = currentTrip.tripEvents) === null || _currentTrip_tripEvents1 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find1 = _currentTrip_tripEvents1.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == tasking.completedTaskID)) === null || _currentTrip_tripEvents_nodes_find1 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find_eventType_Tasking1 = _currentTrip_tripEvents_nodes_find1.eventType_Tasking) === null || _currentTrip_tripEvents_nodes_find_eventType_Tasking1 === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find_eventType_Tasking1.personRescueID) > 0;\n        }\n        if (type === \"TaskingOnScene\" || type === \"TaskingOnTow\") {\n            var _currentTrip_tripEvents2, _latestEvent_eventType_Tasking;\n            var latestEvent;\n            currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents2 = currentTrip.tripEvents) === null || _currentTrip_tripEvents2 === void 0 ? void 0 : _currentTrip_tripEvents2.nodes.filter((event)=>{\n                if ((event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\") {\n                    latestEvent = event;\n                }\n            });\n            return (latestEvent === null || latestEvent === void 0 ? void 0 : (_latestEvent_eventType_Tasking = latestEvent.eventType_Tasking) === null || _latestEvent_eventType_Tasking === void 0 ? void 0 : _latestEvent_eventType_Tasking.personRescueID) > 0;\n        }\n        return (currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find = _currentTrip_tripEvents.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == completedTaskID)) === null || _currentTrip_tripEvents_nodes_find === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find_eventType_Tasking = _currentTrip_tripEvents_nodes_find.eventType_Tasking) === null || _currentTrip_tripEvents_nodes_find_eventType_Tasking === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find_eventType_Tasking.personRescueID) > 0;\n    };\n    const displayVessesRescueFields = ()=>{\n        if (type === \"TaskingOnScene\" && isVesselRescue() || type === \"TaskingOnTow\" && isVesselRescue() || type === \"TaskingComplete\" && isVesselRescue() || tasking.operationType === \"Vessel Mechanical or equipment failure\" || tasking.operationType === \"Vessel adrift\" || tasking.operationType === \"Vessel aground\" || tasking.operationType === \"Capsize\" || tasking.operationType === \"Vessel requiring tow\" || tasking.operationType === \"Flare sighting\" || tasking.operationType === \"Vessel sinking\" || tasking.operationType === \"Collision\" || tasking.operationType === \"Vessel overdue\" || tasking.operationType === \"Vessel - other\") {\n            return true;\n        }\n        return false;\n    };\n    const displayPersonRescueFields = ()=>{\n        if (type === \"TaskingOnScene\" && isPersonRescue() || type === \"TaskingOnTow\" && isPersonRescue() || type === \"TaskingComplete\" && isPersonRescue() || tasking.operationType === \"Person in water\" || tasking.operationType === \"Person lost or missing\" || tasking.operationType === \"Suicide\" || tasking.operationType === \"Medical condition\" || tasking.operationType === \"Person - other\") {\n            return true;\n        }\n        return false;\n    };\n    const handleSaropChange = (e)=>{\n        if (e.target.value == \"on\") {\n            setCurrentIncident(\"sarop\");\n        }\n    };\n    const handleCgopChange = (e)=>{\n        if (e.target.value == \"on\") {\n            setCurrentIncident(\"cgop\");\n        }\n    };\n    const handleUpdateFuelTank = (tank, value)=>{\n        if (tank.capacity < +value) {\n            toast({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: \"Fuel level cannot be higher than tank capacity of \" + tank.capacity\n            });\n            return;\n        }\n        setFuelTankList(fuelTankList.map((item)=>{\n            if (item.id === tank.id) {\n                return {\n                    ...item,\n                    currentLevel: +value\n                };\n            }\n            return item;\n        }));\n        setTasking({\n            ...tasking,\n            fuelLog: false\n        });\n        if (fuelTankBuffer.length > 0 && fuelTankBuffer.filter((item)=>item.tankID === tank.id)) {\n            setFuelTankBuffer(fuelTankBuffer.map((item)=>{\n                if (item.tankID === tank.id) {\n                    return {\n                        ...item,\n                        value: +value\n                    };\n                }\n                return item;\n            }));\n        } else {\n            setFuelTankBuffer([\n                ...fuelTankBuffer,\n                {\n                    tankID: tank.id,\n                    value: +value\n                }\n            ]);\n        }\n    };\n    // Create a debounced version of the update function\n    // const handleUpdateFuelTank = useCallback(\n    //     debounce((tank: any, value: any) => {\n    //         updateFuelTankValue(tank, value)\n    //     }, 500), // 500ms delay\n    //     [fuelTankList, tasking],\n    // )\n    const [updateFuelLog] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_32__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_6__.UPDATE_FUELLOG, {\n        onCompleted: (response)=>{\n            const data = response.updateFuelLog;\n        },\n        onError: (error)=>{\n            console.error(\"Error updating fuel log\", error);\n        }\n    });\n    const [createFuelLog] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_32__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_6__.CREATE_FUELLOG, {\n        onCompleted: (response)=>{\n            const data = response.createFuelLog;\n        },\n        onError: (error)=>{\n            console.error(\"Error creating fuel log\", error);\n        }\n    });\n    const [updateFuelTank] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_32__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_6__.UpdateFuelTank, {\n        onCompleted: (response)=>{\n            const data = response.updateFuelTank;\n            const fuelLog = updatedFuelLogs.filter((log)=>log.fuelTank.id === data.id).sort((a, b)=>new Date(a.date).getTime() - new Date(b.date).getTime())[0];\n            if (fuelLog) {\n                updateFuelLog({\n                    variables: {\n                        input: {\n                            id: fuelLog.id,\n                            fuelAfter: +fuelLog.fuelAfter\n                        }\n                    }\n                });\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error updating fuel tank\", error);\n        }\n    });\n    const updateFuelLogs = function() {\n        let currentID = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0;\n        if (fuelTankList) {\n            Promise.all(fuelTankList === null || fuelTankList === void 0 ? void 0 : fuelTankList.map(async (fuelTank)=>{\n                const variables = {\n                    input: {\n                        id: fuelTank.id,\n                        currentLevel: fuelTank.currentLevel\n                    }\n                };\n                if (!currentEvent) {\n                    if (offline) {\n                        await fuelTankModel.save({\n                            id: fuelTank.id,\n                            currentLevel: fuelTank.currentLevel\n                        });\n                    } else {\n                        updateFuelTank({\n                            variables: variables\n                        });\n                    }\n                }\n                if (currentEvent) {\n                    if (offline) {\n                        var _currentEvent_eventType_Tasking_fuelLog_nodes_find;\n                        await fuelLogModel.save({\n                            id: ((_currentEvent_eventType_Tasking_fuelLog_nodes_find = currentEvent.eventType_Tasking.fuelLog.nodes.find((log)=>{\n                                var _log_fuelTank;\n                                ((_log_fuelTank = log.fuelTank) === null || _log_fuelTank === void 0 ? void 0 : _log_fuelTank.id) === fuelTank.id;\n                            })) === null || _currentEvent_eventType_Tasking_fuelLog_nodes_find === void 0 ? void 0 : _currentEvent_eventType_Tasking_fuelLog_nodes_find.id) || (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_23__.generateUniqueId)(),\n                            fuelTankID: fuelTank.id,\n                            fuelAfter: fuelTank.currentLevel,\n                            date: dayjs__WEBPACK_IMPORTED_MODULE_2___default()().format(\"YYYY-MM-DD\"),\n                            eventType_TaskingID: currentID\n                        });\n                    } else {\n                        updateFuelLog({\n                            variables: {\n                                input: {\n                                    id: currentEvent.eventType_Tasking.fuelLog.nodes.find((log)=>log.fuelTank.id === fuelTank.id).id,\n                                    fuelTankID: fuelTank.id,\n                                    fuelAfter: fuelTank.currentLevel,\n                                    date: dayjs__WEBPACK_IMPORTED_MODULE_2___default()().format(\"YYYY-MM-DD\"),\n                                    eventType_TaskingID: currentID\n                                }\n                            }\n                        });\n                    }\n                } else {\n                    if (offline) {\n                        await fuelLogModel.save({\n                            id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_23__.generateUniqueId)(),\n                            fuelTankID: fuelTank.id,\n                            fuelAfter: fuelTank.currentLevel,\n                            date: dayjs__WEBPACK_IMPORTED_MODULE_2___default()().format(\"YYYY-MM-DD\"),\n                            eventType_TaskingID: currentID\n                        });\n                    } else {\n                        createFuelLog({\n                            variables: {\n                                input: {\n                                    fuelTankID: fuelTank.id,\n                                    fuelAfter: fuelTank.currentLevel,\n                                    date: dayjs__WEBPACK_IMPORTED_MODULE_2___default()().format(\"YYYY-MM-DD\"),\n                                    eventType_TaskingID: currentID\n                                }\n                            }\n                        });\n                    }\n                }\n            }));\n        }\n    };\n    const getInitialFuelLevel = (tank)=>{\n        if (fuelTankBuffer.length > 0) {\n            const fuelTank = fuelTankBuffer.find((item)=>item.tankID === tank.id);\n            if (fuelTank) {\n                return fuelTank.value;\n            }\n        }\n        if (tripReport.length > 0) {\n            var _fuelLogs_filter_sort;\n            const fuelLogs = tripReport.map((trip)=>{\n                return trip.tripEvents.nodes.filter((event)=>event.eventCategory === \"Tasking\" && event.eventType_Tasking.fuelLog.nodes.length > 0 || event.eventCategory === \"RefuellingBunkering\" && event.eventType_RefuellingBunkering.fuelLog.nodes.length > 0 || event.eventCategory === \"PassengerDropFacility\" && event.eventType_PassengerDropFacility.fuelLog.nodes.length > 0).flatMap((event)=>event.eventCategory === \"Tasking\" && event.eventType_Tasking.fuelLog.nodes || event.eventCategory === \"RefuellingBunkering\" && event.eventType_RefuellingBunkering.fuelLog.nodes || event.eventCategory === \"PassengerDropFacility\" && event.eventType_PassengerDropFacility.fuelLog.nodes);\n            }).flat();\n            const lastFuelLog = fuelLogs === null || fuelLogs === void 0 ? void 0 : (_fuelLogs_filter_sort = fuelLogs.filter((log)=>log.fuelTank.id === tank.id).sort((a, b)=>b.id - a.id)) === null || _fuelLogs_filter_sort === void 0 ? void 0 : _fuelLogs_filter_sort[0];\n            if (lastFuelLog) {\n                return lastFuelLog.fuelAfter;\n            }\n        }\n        // if (\n        //     currentTrip &&\n        //     currentTrip?.tripEvents?.nodes?.length > 0 &&\n        //     currentTrip.tripEvents.nodes.find(\n        //         (event: any) =>\n        //             event.eventCategory === 'Tasking' &&\n        //             event.eventType_Tasking.fuelLog.nodes.length > 0,\n        //     )\n        // ) {\n        //     const fuelLog = currentTrip.tripEvents.nodes\n        //         .filter(\n        //             (event: any) =>\n        //                 event.eventCategory === 'Tasking' &&\n        //                 event.eventType_Tasking.fuelLog.nodes.length > 0,\n        //         )\n        //         .sort((a: any, b: any) => b.id - a.id)[0]\n        //         ?.eventType_Tasking.fuelLog.nodes.find(\n        //             (log: any) => log.fuelTank.id === tank.id,\n        //         )\n        //     if (fuelLog) {\n        //         return fuelLog.fuelAfter\n        //     }\n        // }\n        // if (tripReport && tripReport.length > 1) {\n        //     const latestTripFuelLog = tripReport\n        //         .filter((trip: any) => trip.id < currentTrip.id)\n        //         .sort((a: any, b: any) => b.id - a.id)[0]\n        //         ?.tripEvents?.nodes.filter(\n        //             (event: any) =>\n        //                 event.eventCategory === 'Tasking' &&\n        //                 event.eventType_Tasking.fuelLog.nodes.length > 0,\n        //         )\n        //         .sort((a: any, b: any) => b.id - a.id)[0]\n        //         ?.eventType_Tasking.fuelLog.nodes.find(\n        //             (log: any) => log.fuelTank.id === tank.id,\n        //         )\n        //     if (latestTripFuelLog) {\n        //         return latestTripFuelLog.fuelAfter\n        //     }\n        // }\n        const fuelLog = updatedFuelLogs.filter((log)=>log.fuelTank.id === tank.id).sort((a, b)=>new Date(a.date).getTime() - new Date(b.date).getTime())[0];\n        return fuelLog ? +tank.capacity > +fuelLog.fuelAfter ? +fuelLog.fuelAfter : +tank.capacity : +tank.currentLevel;\n    };\n    const getFuelLogs = async (fuelLogIds)=>{\n        if (offline) {\n            const data = await fuelTankModel.getByIds(fuelLogIds);\n        } else {\n            await queryGetFuelLogs({\n                variables: {\n                    id: fuelLogIds\n                }\n            });\n        }\n    };\n    const [queryGetFuelLogs] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_31__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_7__.GET_FUELLOGS, {\n        fetchPolicy: \"no-cache\",\n        onCompleted: (response)=>{\n            const data = response.readFuelLogs.nodes;\n            setUpdatedFuelLogs(data);\n        },\n        onError: (error)=>{\n            console.error(\"getFuelLogs error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        getFuelLogs(fuelLogs.map((item)=>item.id));\n    }, []);\n    var _getPreviousCGOP, _getPreviousSAROP;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"my-4 text-sm font-semibold uppercase\",\n                        children: [\n                            type === \"TaskingStartUnderway\" && \"Tasking start / underway\",\n                            type === \"TaskingPaused\" && \"Tasking paused\",\n                            type === \"TaskingResumed\" && \"Tasking resumed\",\n                            type === \"TaskingOnScene\" && tasking.title,\n                            type === \"TaskingOnTow\" && tasking.title,\n                            type === \"TaskingComplete\" && tasking.title\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 1641,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_29__.P, {\n                        className: \"max-w-[40rem] mb-2\",\n                        children: [\n                            \"Give this tasking a title and choose an operation type.\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                lineNumber: 1652,\n                                columnNumber: 21\n                            }, this),\n                            \"Recording fuel levels goes toward\",\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"fuel reports for allocating to different operations\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                lineNumber: 1654,\n                                columnNumber: 21\n                            }, this),\n                            \".\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 1650,\n                        columnNumber: 17\n                    }, this),\n                    type === \"TaskingOnTow\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_29__.P, {\n                        className: \"max-w-[40rem]\",\n                        children: \"Utilise attached checklist to ensure towing procedure is followed and any risks identified.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 1660,\n                        columnNumber: 21\n                    }, this),\n                    type === \"TaskingOnTow\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CheckFieldLabel, {\n                        type: \"checkbox\",\n                        checked: allChecked,\n                        className: \"w-fit\",\n                        variant: \"success\",\n                        leftContent: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_SquareArrowOutUpRight_X_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                            lineNumber: 1671,\n                            columnNumber: 38\n                        }, void 0),\n                        rightContent: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_SquareArrowOutUpRight_X_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                            lineNumber: 1673,\n                            columnNumber: 29\n                        }, void 0),\n                        onClick: ()=>{\n                            setOpenRiskAnalysis(true);\n                        },\n                        label: \"Towing checklist - risk analysis\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 1666,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_25__.Label, {\n                                        children: \"Time when tasking takes place\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                        lineNumber: 1684,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_time__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        time: time,\n                                        handleTimeChange: handleTimeChange,\n                                        timeID: \"time\",\n                                        fieldName: \"Time\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                        lineNumber: 1685,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                lineNumber: 1682,\n                                columnNumber: 21\n                            }, this),\n                            type === \"TaskingStartUnderway\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_25__.Label, {\n                                        children: \"Title of tasking\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                        lineNumber: 1695,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_24__.Input, {\n                                        id: \"title\",\n                                        name: \"title\",\n                                        type: \"text\",\n                                        value: (tasking === null || tasking === void 0 ? void 0 : tasking.title) ? tasking.title : \"\",\n                                        placeholder: \"Title\",\n                                        onChange: (e)=>{\n                                            setTasking({\n                                                ...tasking,\n                                                title: e.target.value\n                                            });\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                        lineNumber: 1696,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                lineNumber: 1693,\n                                columnNumber: 25\n                            }, this),\n                            fuelTankList && fuelTankList.map((tank)=>/*#__PURE__*/ {\n                                var _tasking_fuelLog_find;\n                                var _tank_currentLevel, _ref;\n                                return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" flex flex-col gap-2 my-4\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-flex\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons_SealogsFuelIcon__WEBPACK_IMPORTED_MODULE_16__.SealogsFuelIcon, {\n                                                    className: \"size-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                    lineNumber: 1717,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_25__.Label, {\n                                                    children: tank.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                    lineNumber: 1718,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                            lineNumber: 1716,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_24__.Input, {\n                                            type: \"number\",\n                                            placeholder: \"Fuel end\",\n                                            value: (_ref = (_tank_currentLevel = tank.currentLevel) !== null && _tank_currentLevel !== void 0 ? _tank_currentLevel : (tasking === null || tasking === void 0 ? void 0 : tasking.fuelLog) ? (_tasking_fuelLog_find = tasking.fuelLog.find((log)=>+log.fuelTank.id === +tank.id)) === null || _tasking_fuelLog_find === void 0 ? void 0 : _tasking_fuelLog_find.fuelAfter : getInitialFuelLevel(tank)) !== null && _ref !== void 0 ? _ref : 0,\n                                            min: 0,\n                                            max: tank.capacity,\n                                            onChange: (e)=>handleUpdateFuelTank(tank, e.target.value)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                            lineNumber: 1720,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, tank.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                    lineNumber: 1713,\n                                    columnNumber: 29\n                                }, this);\n                            }),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat(locked ? \"pointer-events-none\" : \"\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col gap-2 my-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_25__.Label, {\n                                                children: \"Location where tasking takes place\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                lineNumber: 1747,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_location__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                offline: offline,\n                                                setCurrentLocation: setCurrentLocation,\n                                                handleLocationChange: handleLocationChange,\n                                                currentEvent: tripEvent.eventType_Tasking\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                lineNumber: 1748,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                        lineNumber: 1746,\n                                        columnNumber: 25\n                                    }, this),\n                                    type !== \"TaskingPaused\" && type !== \"TaskingResumed\" && displayVessesRescueFields() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"my-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_26__.Textarea, {\n                                            id: \"location-description\",\n                                            rows: 4,\n                                            placeholder: \"Location description\",\n                                            value: locationDescription !== null && locationDescription !== void 0 ? locationDescription : \"\",\n                                            onChange: (e)=>{\n                                                setLocationDescription(e.target.value);\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                            lineNumber: 1759,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                        lineNumber: 1758,\n                                        columnNumber: 29\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                lineNumber: 1745,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat(locked ? \"pointer-events-none\" : \"\"),\n                                children: [\n                                    type === \"TaskingPaused\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"my-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_27__.Combobox, {\n                                                    options: previousDropEvent ? previousDropEvent.filter((group)=>group.eventType_Tasking.status === \"Open\").map((group)=>({\n                                                            value: group.eventType_Tasking.id,\n                                                            label: \"\".concat(group.eventType_Tasking.time, \" - \").concat(group.eventType_Tasking.title)\n                                                        })) : [],\n                                                    value: tasking.pausedTaskID > 0 ? {\n                                                        value: tasking.pausedTaskID,\n                                                        label: \"\".concat(currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find = _currentTrip_tripEvents.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == tasking.pausedTaskID)) === null || _currentTrip_tripEvents_nodes_find === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find_eventType_Tasking = _currentTrip_tripEvents_nodes_find.eventType_Tasking) === null || _currentTrip_tripEvents_nodes_find_eventType_Tasking === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find_eventType_Tasking.time, \" - \").concat(currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents1 = currentTrip.tripEvents) === null || _currentTrip_tripEvents1 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find1 = _currentTrip_tripEvents1.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == tasking.pausedTaskID)) === null || _currentTrip_tripEvents_nodes_find1 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find_eventType_Tasking1 = _currentTrip_tripEvents_nodes_find1.eventType_Tasking) === null || _currentTrip_tripEvents_nodes_find_eventType_Tasking1 === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find_eventType_Tasking1.title)\n                                                    } : taskingPausedValue,\n                                                    onChange: handleTaskingPauseChange,\n                                                    placeholder: \"Select Task to pause\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                    lineNumber: 1777,\n                                                    columnNumber: 37\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                lineNumber: 1776,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"my-4\",\n                                                children: (selectedEvent && content || !selectedEvent) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_editor__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    id: \"comment\",\n                                                    placeholder: \"Comment\",\n                                                    className: \"w-full\",\n                                                    content: content,\n                                                    handleEditorChange: handleEditorChange\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                    lineNumber: 1825,\n                                                    columnNumber: 41\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                                lineNumber: 1822,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true),\n                                    type === \"TaskingComplete\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"my-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_27__.Combobox, {\n                                            options: previousDropEvent ? previousDropEvent.filter((group)=>group.eventType_Tasking.status !== \"Completed\").map((group)=>({\n                                                    value: group.eventType_Tasking.id,\n                                                    label: \"\".concat(group.eventType_Tasking.time, \" - \").concat(group.eventType_Tasking.title)\n                                                })) : [],\n                                            value: tasking.completedTaskID > 0 ? {\n                                                value: tasking.completedTaskID,\n                                                label: \"\".concat(currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents2 = currentTrip.tripEvents) === null || _currentTrip_tripEvents2 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find2 = _currentTrip_tripEvents2.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == tasking.completedTaskID)) === null || _currentTrip_tripEvents_nodes_find2 === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find2.eventType_Tasking.time, \" - \").concat(currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents3 = currentTrip.tripEvents) === null || _currentTrip_tripEvents3 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find3 = _currentTrip_tripEvents3.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == tasking.completedTaskID)) === null || _currentTrip_tripEvents_nodes_find3 === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find3.eventType_Tasking.title)\n                                            } : getPreviousTask(taskingCompleteValue),\n                                            onChange: handleTaskingCompleteChange,\n                                            placeholder: \"Select Task to Close\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                            lineNumber: 1840,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                        lineNumber: 1839,\n                                        columnNumber: 29\n                                    }, this),\n                                    type === \"TaskingResumed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_27__.Combobox, {\n                                        options: previousDropEvent ? previousDropEvent.filter((group)=>group.eventType_Tasking.status === \"Paused\").map((group)=>({\n                                                value: group.eventType_Tasking.id,\n                                                label: \"\".concat(group.eventType_Tasking.time, \" - \").concat(group.eventType_Tasking.title)\n                                            })) : [],\n                                        value: tasking.openTaskID > 0 ? {\n                                            value: tasking.openTaskID,\n                                            label: \"\".concat(currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents4 = currentTrip.tripEvents) === null || _currentTrip_tripEvents4 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find4 = _currentTrip_tripEvents4.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == tasking.openTaskID)) === null || _currentTrip_tripEvents_nodes_find4 === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find4.eventType_Tasking.time, \" - \").concat(currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents5 = currentTrip.tripEvents) === null || _currentTrip_tripEvents5 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_find5 = _currentTrip_tripEvents5.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventType_TaskingID) == tasking.openTaskID)) === null || _currentTrip_tripEvents_nodes_find5 === void 0 ? void 0 : _currentTrip_tripEvents_nodes_find5.eventType_Tasking.title)\n                                        } : taskingResumedValue,\n                                        onChange: handleTaskingGroupChange,\n                                        placeholder: \"Select Task to continue\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                        lineNumber: 1886,\n                                        columnNumber: 29\n                                    }, this),\n                                    type !== \"TaskingPaused\" && type !== \"TaskingResumed\" && type !== \"TaskingComplete\" && type !== \"TaskingOnTow\" && type !== \"TaskingOnScene\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: operationTypes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_27__.Combobox, {\n                                            options: operationTypes.map((type)=>({\n                                                    value: type.value,\n                                                    label: type.label\n                                                })),\n                                            value: {\n                                                value: currentOperationTypeValue(tasking === null || tasking === void 0 ? void 0 : tasking.operationType),\n                                                label: currentOperationTypeLabel(tasking === null || tasking === void 0 ? void 0 : tasking.operationType)\n                                            },\n                                            onChange: handleOperationTypeChange,\n                                            placeholder: \"Operation type\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                            lineNumber: 1933,\n                                            columnNumber: 41\n                                        }, this)\n                                    }, void 0, false)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                lineNumber: 1773,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_29__.P, {\n                                className: \"max-w-[40rem]\",\n                                children: [\n                                    \"Everything else below this section is\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"optional can be completed later\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                        lineNumber: 1957,\n                                        columnNumber: 25\n                                    }, this),\n                                    \". However, all the details loaded here will be used for any tasking reports required.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                lineNumber: 1955,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 1681,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true),\n            type !== \"TaskingPaused\" && type !== \"TaskingResumed\" && displayVessesRescueFields() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessel_rescue_fields__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                offline: offline,\n                geoLocations: geoLocations,\n                selectedEvent: findPreviousEvent(selectedEvent),\n                locationDescription: locationDescription,\n                setLocationDescription: setLocationDescription,\n                closeModal: closeModal,\n                handleSaveParent: handleSave,\n                currentRescueID: findPreviousRescueID(tasking.vesselRescueID),\n                type: type,\n                eventCurrentLocation: {\n                    currentLocation: currentLocation,\n                    geoLocationID: tasking.geoLocationID\n                },\n                locked: locked\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                lineNumber: 1966,\n                columnNumber: 17\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\n            type !== \"TaskingPaused\" && type !== \"TaskingResumed\" && displayPersonRescueFields() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_person_rescue_field__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                offline: offline,\n                geoLocations: geoLocations,\n                selectedEvent: findPreviousEvent(selectedEvent),\n                closeModal: closeModal,\n                handleSaveParent: handleSave,\n                currentRescueID: findPreviousHumanRescueID(tasking.personRescueID),\n                type: type,\n                locked: locked\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                lineNumber: 1990,\n                columnNumber: 17\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\n            type !== \"TaskingPaused\" && type !== \"TaskingResumed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"my-4 text-sm font-semibold uppercase\",\n                        children: \"Incident type / number\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 2007,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_29__.P, {\n                        className: \"max-w-[40rem]\",\n                        children: \"Detail if incident was tasked by Police, RCCNZ or Coastguard and associated incident number if applicable\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 2010,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex w-full items-start  grid-cols-1 md:grid-cols-5 md:grid-rows-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" mt-4 md:my-4 w-full flex items-center space-x-2 py-3\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_28__.Checkbox, {\n                                        id: \"task-cgop\",\n                                        checked: getIsCGOP(tasking === null || tasking === void 0 ? void 0 : tasking.cgop),\n                                        onCheckedChange: (checked)=>{\n                                            if (checked) handleCgopChange({\n                                                target: {\n                                                    value: \"on\"\n                                                }\n                                            });\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                        lineNumber: 2017,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_25__.Label, {\n                                        htmlFor: \"task-cgop\",\n                                        //className=\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 uppercase\"\n                                        className: \"!mb-0\",\n                                        children: \"CoastGuard Rescue\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                        lineNumber: 2027,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                lineNumber: 2015,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" md:my-4 w-full md:col-span-4\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_24__.Input, {\n                                    id: \"cgop\",\n                                    type: \"text\",\n                                    onChange: (e)=>{\n                                        setTasking({\n                                            ...tasking,\n                                            sarop: \"\",\n                                            cgop: e.target.value\n                                        }), setCurrentIncident(\"cgop\");\n                                    },\n                                    value: (_getPreviousCGOP = getPreviousCGOP(tasking === null || tasking === void 0 ? void 0 : tasking.cgop)) !== null && _getPreviousCGOP !== void 0 ? _getPreviousCGOP : \"\",\n                                    \"aria-describedby\": \"cgop-error\",\n                                    required: true,\n                                    placeholder: \"Police / RCCNZ number\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                    lineNumber: 2036,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                lineNumber: 2034,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" mt-4 md:my-4 w-full flex items-center space-x-2 py-3\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_28__.Checkbox, {\n                                        id: \"task-sarop\",\n                                        checked: getIsSAROP(tasking === null || tasking === void 0 ? void 0 : tasking.sarop),\n                                        onCheckedChange: (checked)=>{\n                                            if (checked) handleSaropChange({\n                                                target: {\n                                                    value: \"on\"\n                                                }\n                                            });\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                        lineNumber: 2055,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_25__.Label, {\n                                        htmlFor: \"task-sarop\",\n                                        className: \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 uppercase !mb-0\",\n                                        children: \"SAROP\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                        lineNumber: 2065,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                lineNumber: 2053,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" md:my-4 w-full md:col-span-4\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_24__.Input, {\n                                    id: \"sarop\",\n                                    type: \"text\",\n                                    onChange: (e)=>{\n                                        setTasking({\n                                            ...tasking,\n                                            sarop: e.target.value,\n                                            cgop: \"\"\n                                        }), setCurrentIncident(\"sarop\");\n                                    },\n                                    value: (_getPreviousSAROP = getPreviousSAROP(tasking === null || tasking === void 0 ? void 0 : tasking.sarop)) !== null && _getPreviousSAROP !== void 0 ? _getPreviousSAROP : \"\",\n                                    \"aria-describedby\": \"sarop-error\",\n                                    required: true,\n                                    placeholder: \"Police / RCCNZ number\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                    lineNumber: 2073,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                                lineNumber: 2071,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 2014,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true),\n            (type === \"TaskingPaused\" || type === \"TaskingResumed\") && //<FooterWrapper noBorder>\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_30__.Button, {\n                        variant: \"text\",\n                        iconLeft: _barrel_optimize_names_AlertTriangle_Check_SquareArrowOutUpRight_X_lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"],\n                        onClick: ()=>closeModal(),\n                        children: \"Cancel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 2096,\n                        columnNumber: 21\n                    }, this),\n                    !locked && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_30__.Button, {\n                        iconLeft: _barrel_optimize_names_AlertTriangle_Check_SquareArrowOutUpRight_X_lucide_react__WEBPACK_IMPORTED_MODULE_36__[\"default\"],\n                        onClick: ()=>handleSave(0, 0),\n                        children: selectedEvent ? \"Update\" : \"Save\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 2103,\n                        columnNumber: 25\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                lineNumber: 2095,\n                columnNumber: 17\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_1__.AlertDialogNew, {\n                openDialog: openNewLocationDialog,\n                setOpenDialog: setOpenNewLocationDialog,\n                actionText: \"Add New Location\",\n                handleCreate: handleCreateNewLocation,\n                title: \"Add New Location\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"my-4 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_24__.Input, {\n                            id: \"new-location-title\",\n                            type: \"text\",\n                            \"aria-describedby\": \"title-error\",\n                            required: true,\n                            placeholder: \"Location Title\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                            lineNumber: 2119,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 2118,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_27__.Combobox, {\n                            id: \"parent-location\",\n                            options: locations || [],\n                            onChange: handleParentLocationChange,\n                            placeholder: \"Parent Location (Optional)\",\n                            buttonClassName: \"w-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                            lineNumber: 2128,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 2127,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_24__.Input, {\n                            id: \"new-location-latitude\",\n                            type: \"text\",\n                            defaultValue: location.latitude,\n                            \"aria-describedby\": \"latitude-error\",\n                            required: true,\n                            placeholder: \"Latitude\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                            lineNumber: 2137,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 2136,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_24__.Input, {\n                            id: \"new-location-longitude\",\n                            type: \"text\",\n                            defaultValue: location.longitude,\n                            \"aria-describedby\": \"longitude-error\",\n                            required: true,\n                            placeholder: \"Longitude\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                            lineNumber: 2147,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                        lineNumber: 2146,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                lineNumber: 2112,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_risk_analysis__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                offline: offline,\n                selectedEvent: findPreviousEvent(selectedEvent),\n                crewMembers: members,\n                towingChecklistID: towingChecklistID,\n                setTowingChecklistID: setTowingChecklistID,\n                setAllChecked: setAllChecked,\n                onSidebarClose: ()=>setOpenRiskAnalysis(false),\n                logBookConfig: undefined,\n                currentTrip: undefined,\n                open: openRiskAnalysis,\n                onOpenChange: setOpenRiskAnalysis\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n                lineNumber: 2158,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\tasking.tsx\",\n        lineNumber: 1639,\n        columnNumber: 9\n    }, this);\n}\n_s(Tasking, \"FkSGTOI8nWEa7r9P1zE0J+7ZiEs=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_15__.useSearchParams,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_31__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_31__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_31__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_32__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_32__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_32__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_32__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_32__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_32__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_31__.useLazyQuery\n    ];\n});\n_c = Tasking;\nvar _c;\n$RefreshReg$(_c, \"Tasking\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/forms/tasking.tsx\n"));

/***/ })

});