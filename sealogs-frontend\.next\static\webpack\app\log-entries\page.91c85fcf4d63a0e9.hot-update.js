"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/forms/risk-analysis.tsx":
/*!****************************************************!*\
  !*** ./src/app/ui/logbook/forms/risk-analysis.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ RiskAnalysis; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _editor__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../editor */ \"(app-pages-browser)/./src/app/ui/editor.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_offline_models_logBookEntry__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/offline/models/logBookEntry */ \"(app-pages-browser)/./src/app/offline/models/logBookEntry.js\");\n/* harmony import */ var _app_offline_models_towingChecklist__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/offline/models/towingChecklist */ \"(app-pages-browser)/./src/app/offline/models/towingChecklist.js\");\n/* harmony import */ var _app_offline_models_riskFactor__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/offline/models/riskFactor */ \"(app-pages-browser)/./src/app/offline/models/riskFactor.js\");\n/* harmony import */ var _app_offline_models_crewMembers_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/offline/models/crewMembers_LogBookEntrySection */ \"(app-pages-browser)/./src/app/offline/models/crewMembers_LogBookEntrySection.js\");\n/* harmony import */ var _app_offline_models_eventType_Tasking__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/offline/models/eventType_Tasking */ \"(app-pages-browser)/./src/app/offline/models/eventType_Tasking.js\");\n/* harmony import */ var _app_offline_models_mitigationStrategy__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/offline/models/mitigationStrategy */ \"(app-pages-browser)/./src/app/offline/models/mitigationStrategy.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var lodash_uniqBy__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! lodash/uniqBy */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/uniqBy.js\");\n/* harmony import */ var lodash_uniqBy__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(lodash_uniqBy__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var _components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/alert-dialog-new */ \"(app-pages-browser)/./src/components/ui/alert-dialog-new.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _components_ui_risk_analysis__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/ui/risk-analysis */ \"(app-pages-browser)/./src/components/ui/risk-analysis/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n// Import React and hooks\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Risk Analysis components\n\nfunction RiskAnalysis(param) {\n    let { selectedEvent = false, onSidebarClose, logBookConfig, currentTrip, crewMembers = false, towingChecklistID = 0, setTowingChecklistID, offline = false, setAllChecked, open = false, onOpenChange } = param;\n    var _riskAnalysis_riskFactors;\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useSearchParams)();\n    var _searchParams_get;\n    const logentryID = parseInt((_searchParams_get = searchParams.get(\"logentryID\")) !== null && _searchParams_get !== void 0 ? _searchParams_get : \"0\");\n    var _searchParams_get1;\n    const vesselID = (_searchParams_get1 = searchParams.get(\"vesselID\")) !== null && _searchParams_get1 !== void 0 ? _searchParams_get1 : 0;\n    const [riskAnalysis, setRiskAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [riskBuffer, setRiskBuffer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openRiskDialog, setOpenRiskDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentRisk, setCurrentRisk] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [allRisks, setAllRisks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [allRiskFactors, setAllRiskFactors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [riskValue, setRiskValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Using setUpdateStrategy but not reading updateStrategy\n    const [, setUpdateStrategy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Unused state variables commented out\n    // const [strategyEditor, setstrategyEditor] = useState<any>(false)\n    const [openRecommendedstrategy, setOpenRecommendedstrategy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [recommendedStratagies, setRecommendedStratagies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentStrategies, setCurrentStrategies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Using setRecommendedstrategy but not reading recommendedstrategy\n    const [, setRecommendedstrategy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [riskToDelete, setRiskToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openDeleteConfirmation, setOpenDeleteConfirmation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [logbook, setLogbook] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [members, setMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [edit_risks, setEdit_risks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [delete_risks, setDelete_risks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editTaskingRisk, setEditTaskingRisk] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast)();\n    const logBookEntryModel = new _app_offline_models_logBookEntry__WEBPACK_IMPORTED_MODULE_9__[\"default\"]();\n    const towingChecklistModel = new _app_offline_models_towingChecklist__WEBPACK_IMPORTED_MODULE_10__[\"default\"]();\n    const riskFactorModel = new _app_offline_models_riskFactor__WEBPACK_IMPORTED_MODULE_11__[\"default\"]();\n    const crewMemberModel = new _app_offline_models_crewMembers_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_12__[\"default\"]();\n    const taskingModel = new _app_offline_models_eventType_Tasking__WEBPACK_IMPORTED_MODULE_13__[\"default\"]();\n    const mitigationStrategyModel = new _app_offline_models_mitigationStrategy__WEBPACK_IMPORTED_MODULE_14__[\"default\"]();\n    const [selectedAuthor, setSelectedAuthor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const init_permissions = ()=>{\n        if (permissions) {\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__.hasPermission)(\"EDIT_RISK\", permissions)) {\n                setEdit_risks(true);\n            } else {\n                setEdit_risks(false);\n            }\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__.hasPermission)(\"DELETE_RISK\", permissions)) {\n                setDelete_risks(true);\n            } else {\n                setDelete_risks(false);\n            }\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__.hasPermission)(\"EDIT_LOGBOOKENTRY_RISK_ANALYSIS\", permissions)) {\n                setEditTaskingRisk(true);\n            } else {\n                setEditTaskingRisk(false);\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__.getPermissions);\n        init_permissions();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        init_permissions();\n    }, [\n        permissions\n    ]);\n    const [getSectionCrewMembers_LogBookEntrySection, // Unused loading state\n    {}] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_21__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__.CrewMembers_LogBookEntrySection, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            let data = response.readCrewMembers_LogBookEntrySections.nodes;\n            const crewMembers = data.map((member)=>{\n                var _member_crewMember_firstName, _member_crewMember_surname;\n                return {\n                    label: \"\".concat((_member_crewMember_firstName = member.crewMember.firstName) !== null && _member_crewMember_firstName !== void 0 ? _member_crewMember_firstName : \"\", \" \").concat((_member_crewMember_surname = member.crewMember.surname) !== null && _member_crewMember_surname !== void 0 ? _member_crewMember_surname : \"\"),\n                    value: member.crewMember.id\n                };\n            }).filter((member)=>member.value != logbook.master.id);\n            setMembers(lodash_uniqBy__WEBPACK_IMPORTED_MODULE_16___default()([\n                ...members,\n                ...crewMembers\n            ], \"value\"));\n        },\n        onError: (error)=>{\n            console.error(\"CrewMembers_LogBookEntrySection error\", error);\n        }\n    });\n    const handleSetLogbook = async (logbook)=>{\n        setLogbook(logbook);\n        var _logbook_master_firstName, _logbook_master_surname;\n        const master = {\n            label: \"\".concat((_logbook_master_firstName = logbook.master.firstName) !== null && _logbook_master_firstName !== void 0 ? _logbook_master_firstName : \"\", \" \").concat((_logbook_master_surname = logbook.master.surname) !== null && _logbook_master_surname !== void 0 ? _logbook_master_surname : \"\"),\n            value: logbook.master.id\n        };\n        if (+master.value > 0) {\n            if (Array.isArray(members)) {\n                setMembers(lodash_uniqBy__WEBPACK_IMPORTED_MODULE_16___default()([\n                    ...members,\n                    master\n                ], \"value\"));\n            } else {\n                setMembers([\n                    master\n                ]);\n            }\n        }\n        const sections = logbook.logBookEntrySections.nodes.filter((node)=>{\n            return node.className === \"SeaLogs\\\\CrewMembers_LogBookEntrySection\";\n        });\n        if (sections) {\n            const sectionIDs = sections.map((section)=>section.id);\n            if ((sectionIDs === null || sectionIDs === void 0 ? void 0 : sectionIDs.length) > 0) {\n                if (offline) {\n                    const data = await crewMemberModel.getByIds(sectionIDs);\n                    const crewMembers = data.map((member)=>{\n                        var _member_crewMember_firstName, _member_crewMember_surname;\n                        return {\n                            label: \"\".concat((_member_crewMember_firstName = member.crewMember.firstName) !== null && _member_crewMember_firstName !== void 0 ? _member_crewMember_firstName : \"\", \" \").concat((_member_crewMember_surname = member.crewMember.surname) !== null && _member_crewMember_surname !== void 0 ? _member_crewMember_surname : \"\"),\n                            value: member.crewMember.id\n                        };\n                    });\n                    if (Array.isArray(members)) {\n                        setMembers(lodash_uniqBy__WEBPACK_IMPORTED_MODULE_16___default()([\n                            ...members,\n                            ...crewMembers\n                        ], \"value\"));\n                    } else {\n                        setMembers(crewMembers);\n                    }\n                } else {\n                    getSectionCrewMembers_LogBookEntrySection({\n                        variables: {\n                            filter: {\n                                id: {\n                                    in: sectionIDs\n                                }\n                            }\n                        }\n                    });\n                }\n            }\n        }\n    };\n    if (logentryID > 0 && !offline) {\n        (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_7__.getLogBookEntryByID)(+logentryID, handleSetLogbook);\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (crewMembers) {\n            const members = crewMembers.map((member)=>{\n                var _member_crewMember_firstName, _member_crewMember_surname;\n                return {\n                    label: \"\".concat((_member_crewMember_firstName = member.crewMember.firstName) !== null && _member_crewMember_firstName !== void 0 ? _member_crewMember_firstName : \"\", \" \").concat((_member_crewMember_surname = member.crewMember.surname) !== null && _member_crewMember_surname !== void 0 ? _member_crewMember_surname : \"\"),\n                    value: member.crewMemberID\n                };\n            });\n            setMembers(members);\n        }\n    }, [\n        crewMembers\n    ]);\n    const handleTaskingRiskFieldChange = (field)=>async (check)=>{\n            if (!editTaskingRisk || !edit_risks) {\n                toast({\n                    title: \"Permission Error\",\n                    description: \"You do not have permission to edit this section\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            setRiskBuffer({\n                ...riskBuffer,\n                [field]: check ? \"on\" : \"off\"\n            });\n            if (+(riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.id) > 0) {\n                if (offline) {\n                    const data = await towingChecklistModel.save({\n                        id: riskAnalysis.id,\n                        [field]: check ? true : false\n                    });\n                    const towingChecklistData = await towingChecklistModel.getById(data.id);\n                    setRiskAnalysis(towingChecklistData);\n                } else {\n                    updateTowingChecklist({\n                        variables: {\n                            input: {\n                                id: riskAnalysis.id,\n                                [field]: check ? true : false\n                            }\n                        }\n                    });\n                }\n            }\n        };\n    const [updateTowingChecklist] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.UpdateTowingChecklist, {\n        onCompleted: (data)=>{\n            getRiskAnalysis({\n                variables: {\n                    id: data.updateTowingChecklist.id\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const fields = [\n        {\n            name: \"ConductSAP\",\n            label: \"Conduct SAP\",\n            value: \"conductSAP\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.conductSAP) ? riskBuffer.conductSAP === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.conductSAP,\n            handleChange: handleTaskingRiskFieldChange(\"conductSAP\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Conduct SAP prior to approaching the vessel.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 308,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Check for fittings on the vessel that could damage the CRV when coming alongside.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 307,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            name: \"InvestigateNatureOfIssue\",\n            label: \"Investigate nature of the issue\",\n            value: \"investigateNatureOfIssue\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.investigateNatureOfIssue) ? riskBuffer.investigateNatureOfIssue === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.investigateNatureOfIssue,\n            handleChange: handleTaskingRiskFieldChange(\"investigateNatureOfIssue\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Ascertain the nature of the problem, any damage, or taking on water.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 328,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Does a crew member need to go on board the other vessel to assist?\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 327,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            name: \"EveryoneOnBoardOk\",\n            label: \"Everyone on board ok?\",\n            value: \"everyoneOnBoardOk\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.everyoneOnBoardOk) ? riskBuffer.everyoneOnBoardOk === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.everyoneOnBoardOk,\n            handleChange: handleTaskingRiskFieldChange(\"everyoneOnBoardOk\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Check how many people are aboard, ensure everyone is accounted for.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 349,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Check for injuries or medical assistance required.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 348,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            name: \"RudderToMidshipsAndTrimmed\",\n            label: \"Rudder to midships and trimmed appropriately\",\n            value: \"rudderToMidshipsAndTrimmed\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.rudderToMidshipsAndTrimmed) ? riskBuffer.rudderToMidshipsAndTrimmed === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.rudderToMidshipsAndTrimmed,\n            handleChange: handleTaskingRiskFieldChange(\"rudderToMidshipsAndTrimmed\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Check steering isn’t impaired in any way and have the rudder secured amidships or have the vessel steer for the stern of CRV.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 371,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Check the vessel is optimally trimmed for towing.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 376,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 370,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            name: \"LifejacketsOn\",\n            label: \"Lifejackets on\",\n            value: \"lifejacketsOn\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.lifejacketsOn) ? riskBuffer.lifejacketsOn === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.lifejacketsOn,\n            handleChange: handleTaskingRiskFieldChange(\"lifejacketsOn\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: \"Request that everyone wears a lifejacket.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                    lineNumber: 390,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 389,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            name: \"CommunicationsEstablished\",\n            label: \"Communications Established\",\n            value: \"communicationsEstablished\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.communicationsEstablished) ? riskBuffer.communicationsEstablished === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.communicationsEstablished,\n            handleChange: handleTaskingRiskFieldChange(\"communicationsEstablished\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Ensure that communications have been established and checked prior to beginning the tow, i.e., VHF, hand signals, and/or light signals if the tow is to be conducted at night.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 406,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Ensure there is agreement on where to tow the vessel to.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 412,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 405,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            name: \"SecureAndSafeTowing\",\n            label: \"Secure and safe towing\",\n            value: \"secureAndSafeTowing\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.secureAndSafeTowing) ? riskBuffer.secureAndSafeTowing === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.secureAndSafeTowing,\n            handleChange: handleTaskingRiskFieldChange(\"secureAndSafeTowing\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Towline securely attached\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 428,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Ensure everything on board is stowed and secure.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 429,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Confirm waterline length/cruising speed of the vessel (safe tow speed).\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 430,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Confirm attachment points for the towline.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 434,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Confirm that the towline is securely attached.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 435,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Ensure that no one on the other vessel is in close proximity to the towline before commencing the tow.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 436,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Turn on CRV towing lights and other vessel’s navigation lights.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 440,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Post towline lookout with responsibility for quick release of the tow / must carry or have a knife handy.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 444,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 427,\n                columnNumber: 17\n            }, this)\n        }\n    ];\n    const createOfflineTowingChecklist = async ()=>{\n        var _selectedEvent_eventType_Tasking;\n        const data = await towingChecklistModel.save({\n            id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_15__.generateUniqueId)()\n        });\n        setTowingChecklistID(+data.id);\n        await taskingModel.save({\n            id: towingChecklistID > 0 ? towingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking === void 0 ? void 0 : _selectedEvent_eventType_Tasking.id,\n            towingChecklistID: +data.id\n        });\n        const towingChecklistData = await towingChecklistModel.getById(data.id);\n        setRiskAnalysis(towingChecklistData);\n    };\n    const offlineGetRiskAnalysis = async ()=>{\n        var _selectedEvent_eventType_Tasking_towingChecklist, _selectedEvent_eventType_Tasking;\n        const data = await towingChecklistModel.getById(towingChecklistID > 0 ? towingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking === void 0 ? void 0 : (_selectedEvent_eventType_Tasking_towingChecklist = _selectedEvent_eventType_Tasking.towingChecklist) === null || _selectedEvent_eventType_Tasking_towingChecklist === void 0 ? void 0 : _selectedEvent_eventType_Tasking_towingChecklist.id);\n        setRiskAnalysis(data);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedEvent || towingChecklistID > 0) {\n            var _selectedEvent_eventType_Tasking_towingChecklist, _selectedEvent_eventType_Tasking;\n            if ((selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking === void 0 ? void 0 : (_selectedEvent_eventType_Tasking_towingChecklist = _selectedEvent_eventType_Tasking.towingChecklist) === null || _selectedEvent_eventType_Tasking_towingChecklist === void 0 ? void 0 : _selectedEvent_eventType_Tasking_towingChecklist.id) > 0 || towingChecklistID > 0) {\n                if (offline) {\n                    offlineGetRiskAnalysis();\n                } else {\n                    var _selectedEvent_eventType_Tasking_towingChecklist1, _selectedEvent_eventType_Tasking1;\n                    getRiskAnalysis({\n                        variables: {\n                            id: towingChecklistID > 0 ? towingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking1 = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking1 === void 0 ? void 0 : (_selectedEvent_eventType_Tasking_towingChecklist1 = _selectedEvent_eventType_Tasking1.towingChecklist) === null || _selectedEvent_eventType_Tasking_towingChecklist1 === void 0 ? void 0 : _selectedEvent_eventType_Tasking_towingChecklist1.id\n                        }\n                    });\n                }\n            } else {\n                if (offline) {\n                    createOfflineTowingChecklist();\n                } else {\n                    createTowingChecklist({\n                        variables: {\n                            input: {}\n                        }\n                    });\n                }\n            }\n        }\n    }, [\n        selectedEvent,\n        towingChecklistID\n    ]);\n    const offlineMount = async ()=>{\n        var _Array_from;\n        const data = await riskFactorModel.getByFieldID(\"type\", \"TowingChecklist\");\n        const risks = (_Array_from = Array.from(new Set(data.map((risk)=>risk.title)))) === null || _Array_from === void 0 ? void 0 : _Array_from.map((risk)=>({\n                label: risk,\n                value: risk\n            }));\n        setAllRisks(risks);\n        setAllRiskFactors(data);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (offline) {\n            offlineMount();\n        } else {\n            getRiskFactors({\n                variables: {\n                    filter: {\n                        type: {\n                            eq: \"TowingChecklist\"\n                        }\n                    }\n                }\n            });\n        }\n    }, []);\n    const [getRiskFactors] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_21__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__.GetRiskFactors, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            var _Array_from, _data_readRiskFactors_nodes;\n            const risks = (_Array_from = Array.from(new Set((_data_readRiskFactors_nodes = data.readRiskFactors.nodes) === null || _data_readRiskFactors_nodes === void 0 ? void 0 : _data_readRiskFactors_nodes.map((risk)=>risk.title)))) === null || _Array_from === void 0 ? void 0 : _Array_from.map((risk)=>({\n                    label: risk,\n                    value: risk\n                }));\n            setAllRisks(risks);\n            setAllRiskFactors(data.readRiskFactors.nodes);\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [getRiskAnalysis] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_21__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__.TowingChecklist, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            setRiskAnalysis(data.readOneTowingChecklist);\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [createTowingChecklist] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.CreateTowingChecklist, {\n        onCompleted: (data)=>{\n            var _selectedEvent_eventType_Tasking;\n            setTowingChecklistID(+data.createTowingChecklist.id);\n            updateEvent({\n                variables: {\n                    input: {\n                        id: towingChecklistID > 0 ? towingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking === void 0 ? void 0 : _selectedEvent_eventType_Tasking.id,\n                        towingChecklistID: +data.createTowingChecklist.id\n                    }\n                }\n            });\n            getRiskAnalysis({\n                variables: {\n                    id: data.createTowingChecklist.id\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [updateEvent] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.UpdateEventType_Tasking, {\n        onCompleted: ()=>{},\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const updateRiskAnalysisMember = async (memberID)=>{\n        if (!editTaskingRisk || !edit_risks) {\n            toast({\n                title: \"Permission Error\",\n                description: \"You do not have permission to edit this section\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        if (offline) {\n            const data = await towingChecklistModel.save({\n                id: riskAnalysis.id,\n                memberID: memberID\n            });\n            const towingChecklistData = await towingChecklistModel.getById(data.id);\n            setRiskAnalysis(towingChecklistData);\n        } else {\n            updateTowingChecklist({\n                variables: {\n                    input: {\n                        id: riskAnalysis.id,\n                        memberID: memberID\n                    }\n                }\n            });\n        }\n    };\n    const handleEditorChange = (newContent)=>{\n        setContent(newContent);\n    };\n    const riskImpacts = [\n        {\n            value: \"Low\",\n            label: \"Low impact\"\n        },\n        {\n            value: \"Medium\",\n            label: \"Medium impact\"\n        },\n        {\n            value: \"High\",\n            label: \"High impact\"\n        },\n        {\n            value: \"Severe\",\n            label: \"Severe impact\"\n        }\n    ];\n    const handleSaveRisk = async ()=>{\n        if (currentRisk.id > 0) {\n            if (offline) {\n                var _Array_from, _selectedEvent_eventType_Tasking_towingChecklist, _selectedEvent_eventType_Tasking;\n                await riskFactorModel.save({\n                    id: currentRisk.id,\n                    type: \"TowingChecklist\",\n                    title: currentRisk.title,\n                    impact: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact : \"Low\",\n                    probability: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability : 5,\n                    mitigationStrategy: currentStrategies.length > 0 ? currentStrategies.map((s)=>s.id).join(\",\") : \"\",\n                    towingChecklistID: riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.id\n                });\n                setOpenRiskDialog(false);\n                const data = await riskFactorModel.getByFieldID(\"type\", \"TowingChecklist\");\n                const risks = (_Array_from = Array.from(new Set(data.map((risk)=>risk.title)))) === null || _Array_from === void 0 ? void 0 : _Array_from.map((risk)=>({\n                        label: risk,\n                        value: risk\n                    }));\n                setAllRisks(risks);\n                setAllRiskFactors(data);\n                const towingChecklistData = await towingChecklistModel.getById(towingChecklistID > 0 ? towingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking === void 0 ? void 0 : (_selectedEvent_eventType_Tasking_towingChecklist = _selectedEvent_eventType_Tasking.towingChecklist) === null || _selectedEvent_eventType_Tasking_towingChecklist === void 0 ? void 0 : _selectedEvent_eventType_Tasking_towingChecklist.id);\n                setRiskAnalysis(towingChecklistData);\n            } else {\n                updateRiskFactor({\n                    variables: {\n                        input: {\n                            id: currentRisk.id,\n                            type: \"TowingChecklist\",\n                            title: currentRisk.title,\n                            impact: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact : \"Low\",\n                            probability: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability : 5,\n                            mitigationStrategy: currentStrategies.length > 0 ? currentStrategies.map((s)=>s.id).join(\",\") : \"\",\n                            towingChecklistID: riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.id\n                        }\n                    }\n                });\n            }\n        } else {\n            if (offline) {\n                var _Array_from1, _selectedEvent_eventType_Tasking_towingChecklist1, _selectedEvent_eventType_Tasking1;\n                await riskFactorModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_15__.generateUniqueId)(),\n                    type: \"TowingChecklist\",\n                    title: currentRisk.title,\n                    impact: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact : \"Low\",\n                    probability: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability : 5,\n                    mitigationStrategy: currentStrategies.length > 0 ? currentStrategies.map((s)=>s.id).join(\",\") : \"\",\n                    towingChecklistID: riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.id,\n                    vesselID: vesselID\n                });\n                setOpenRiskDialog(false);\n                const data = await riskFactorModel.getByFieldID(\"type\", \"TowingChecklist\");\n                const risks = (_Array_from1 = Array.from(new Set(data.map((risk)=>risk.title)))) === null || _Array_from1 === void 0 ? void 0 : _Array_from1.map((risk)=>({\n                        label: risk,\n                        value: risk\n                    }));\n                setAllRisks(risks);\n                setAllRiskFactors(data);\n                const towingChecklistData = await towingChecklistModel.getById(towingChecklistID > 0 ? towingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking1 = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking1 === void 0 ? void 0 : (_selectedEvent_eventType_Tasking_towingChecklist1 = _selectedEvent_eventType_Tasking1.towingChecklist) === null || _selectedEvent_eventType_Tasking_towingChecklist1 === void 0 ? void 0 : _selectedEvent_eventType_Tasking_towingChecklist1.id);\n                setRiskAnalysis(towingChecklistData);\n            } else {\n                createRiskFactor({\n                    variables: {\n                        input: {\n                            type: \"TowingChecklist\",\n                            title: currentRisk.title,\n                            impact: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact : \"Low\",\n                            probability: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability : 5,\n                            mitigationStrategy: currentStrategies.length > 0 ? currentStrategies.map((s)=>s.id).join(\",\") : \"\",\n                            towingChecklistID: riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.id,\n                            vesselID: vesselID\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const [createMitigationStrategy] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.CreateMitigationStrategy, {\n        onCompleted: (data)=>{\n            setCurrentStrategies([\n                ...currentStrategies,\n                {\n                    id: data.createMitigationStrategy.id,\n                    strategy: content\n                }\n            ]);\n            setContent(\"\");\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [createRiskFactor] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.CreateRiskFactor, {\n        onCompleted: ()=>{\n            var _selectedEvent_eventType_Tasking_towingChecklist, _selectedEvent_eventType_Tasking;\n            setOpenRiskDialog(false);\n            getRiskFactors({\n                variables: {\n                    filter: {\n                        type: {\n                            eq: \"TowingChecklist\"\n                        }\n                    }\n                }\n            });\n            getRiskAnalysis({\n                variables: {\n                    id: towingChecklistID > 0 ? towingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking === void 0 ? void 0 : (_selectedEvent_eventType_Tasking_towingChecklist = _selectedEvent_eventType_Tasking.towingChecklist) === null || _selectedEvent_eventType_Tasking_towingChecklist === void 0 ? void 0 : _selectedEvent_eventType_Tasking_towingChecklist.id\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [updateRiskFactor] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.UpdateRiskFactor, {\n        onCompleted: ()=>{\n            var _selectedEvent_eventType_Tasking_towingChecklist, _selectedEvent_eventType_Tasking;\n            setOpenRiskDialog(false);\n            getRiskFactors({\n                variables: {\n                    filter: {\n                        type: {\n                            eq: \"TowingChecklist\"\n                        }\n                    }\n                }\n            });\n            getRiskAnalysis({\n                variables: {\n                    id: towingChecklistID > 0 ? towingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking === void 0 ? void 0 : (_selectedEvent_eventType_Tasking_towingChecklist = _selectedEvent_eventType_Tasking.towingChecklist) === null || _selectedEvent_eventType_Tasking_towingChecklist === void 0 ? void 0 : _selectedEvent_eventType_Tasking_towingChecklist.id\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const handleRiskValue = (v)=>{\n        setCurrentRisk({\n            ...currentRisk,\n            title: v === null || v === void 0 ? void 0 : v.value\n        });\n        setRiskValue({\n            value: v.value,\n            label: v.value\n        });\n        if ((allRiskFactors === null || allRiskFactors === void 0 ? void 0 : allRiskFactors.filter((risk)=>{\n            var _risk_mitigationStrategy_nodes;\n            return risk.title === v.value && ((_risk_mitigationStrategy_nodes = risk.mitigationStrategy.nodes) === null || _risk_mitigationStrategy_nodes === void 0 ? void 0 : _risk_mitigationStrategy_nodes.length) > 0;\n        }).length) > 0) {\n            setRecommendedStratagies(Array.from(new Set(allRiskFactors === null || allRiskFactors === void 0 ? void 0 : allRiskFactors.filter((r)=>{\n                var _r_mitigationStrategy_nodes;\n                return r.title === v.value && ((_r_mitigationStrategy_nodes = r.mitigationStrategy.nodes) === null || _r_mitigationStrategy_nodes === void 0 ? void 0 : _r_mitigationStrategy_nodes.length) > 0;\n            }).map((r)=>r.mitigationStrategy.nodes)[0].map((s)=>({\n                    id: s.id,\n                    strategy: s.strategy\n                })))));\n        } else {\n            setRecommendedStratagies(false);\n        }\n    };\n    // This function is not used directly in the component but is kept for reference\n    // and potential future use\n    // const handleCreateRisk = (inputValue: any) => {\n    //     setCurrentRisk({\n    //         ...currentRisk,\n    //         title: inputValue,\n    //     })\n    //     setRiskValue({ value: inputValue, label: inputValue })\n    //     if (allRisks) {\n    //         const risk = [...allRisks, { value: inputValue, label: inputValue }]\n    //         setAllRisks(risk)\n    //     } else {\n    //         setAllRisks([{ value: inputValue, label: inputValue }])\n    //     }\n    // }\n    const handleDeleteRisk = async ()=>{\n        if (offline) {\n            var _Array_from, _selectedEvent_eventType_Tasking_towingChecklist, _selectedEvent_eventType_Tasking;\n            await riskFactorModel.save({\n                id: riskToDelete.id,\n                towingChecklistID: 0,\n                vesselID: 0\n            });\n            setOpenRiskDialog(false);\n            const data = await riskFactorModel.getByFieldID(\"type\", \"TowingChecklist\");\n            const risks = (_Array_from = Array.from(new Set(data.map((risk)=>risk.title)))) === null || _Array_from === void 0 ? void 0 : _Array_from.map((risk)=>({\n                    label: risk,\n                    value: risk\n                }));\n            setAllRisks(risks);\n            setAllRiskFactors(data);\n            const towingChecklistData = await towingChecklistModel.getById(towingChecklistID > 0 ? towingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking === void 0 ? void 0 : (_selectedEvent_eventType_Tasking_towingChecklist = _selectedEvent_eventType_Tasking.towingChecklist) === null || _selectedEvent_eventType_Tasking_towingChecklist === void 0 ? void 0 : _selectedEvent_eventType_Tasking_towingChecklist.id);\n            setRiskAnalysis(towingChecklistData);\n        } else {\n            updateRiskFactor({\n                variables: {\n                    input: {\n                        id: riskToDelete.id,\n                        towingChecklistID: 0,\n                        vesselID: 0\n                    }\n                }\n            });\n        }\n        setOpenDeleteConfirmation(false);\n    };\n    const handleSetCurrentStrategies = (strategy)=>{\n        if (currentStrategies.length > 0) {\n            if (currentStrategies.find((s)=>s.id === strategy.id)) {\n                setCurrentStrategies(currentStrategies.filter((s)=>s.id !== strategy.id));\n            } else {\n                setCurrentStrategies([\n                    ...currentStrategies,\n                    strategy\n                ]);\n            }\n        } else {\n            setCurrentStrategies([\n                strategy\n            ]);\n        }\n    };\n    const handleNewStrategy = async ()=>{\n        if (content) {\n            if (offline) {\n                const data = await mitigationStrategyModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_15__.generateUniqueId)(),\n                    strategy: content\n                });\n                const newStrategies = [\n                    ...currentStrategies,\n                    {\n                        id: data.id,\n                        strategy: content\n                    }\n                ];\n                setCurrentRisk({\n                    ...currentRisk,\n                    mitigationStrategy: {\n                        nodes: newStrategies\n                    }\n                });\n                setCurrentStrategies(newStrategies);\n                setContent(\"\");\n            } else {\n                createMitigationStrategy({\n                    variables: {\n                        input: {\n                            strategy: content\n                        }\n                    }\n                });\n            }\n        }\n        setOpenRecommendedstrategy(false);\n    };\n    const handleSetRiskValue = (v)=>{\n        setRiskValue({\n            value: v.title,\n            label: v.title\n        });\n        if (v.mitigationStrategy.nodes) {\n            setCurrentStrategies(v.mitigationStrategy.nodes);\n        }\n        if ((allRiskFactors === null || allRiskFactors === void 0 ? void 0 : allRiskFactors.filter((risk)=>{\n            var _risk_mitigationStrategy_nodes;\n            return risk.title === v.title && ((_risk_mitigationStrategy_nodes = risk.mitigationStrategy.nodes) === null || _risk_mitigationStrategy_nodes === void 0 ? void 0 : _risk_mitigationStrategy_nodes.length) > 0;\n        }).length) > 0) {\n            setRecommendedStratagies(Array.from(new Set(allRiskFactors === null || allRiskFactors === void 0 ? void 0 : allRiskFactors.filter((r)=>{\n                var _r_mitigationStrategy_nodes;\n                return r.title === v.title && ((_r_mitigationStrategy_nodes = r.mitigationStrategy.nodes) === null || _r_mitigationStrategy_nodes === void 0 ? void 0 : _r_mitigationStrategy_nodes.length) > 0;\n            }).map((r)=>r.mitigationStrategy.nodes)[0].map((s)=>({\n                    id: s.id,\n                    strategy: s.strategy\n                })))));\n        } else {\n            setRecommendedStratagies(false);\n        }\n    };\n    const offlineGetLogBookEntryByID = async ()=>{\n        const logbook = await logBookEntryModel.getById(logentryID);\n        handleSetLogbook(logbook);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (offline) {\n            offlineGetLogBookEntryByID();\n        }\n    }, [\n        offline\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (members && riskAnalysis) {\n            const member = members.find((member)=>member.value == riskAnalysis.member.id);\n            setSelectedAuthor(member);\n        }\n    }, [\n        members,\n        riskAnalysis\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setAllChecked(fields.every((field)=>field.checked));\n    }, [\n        fields\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_risk_analysis__WEBPACK_IMPORTED_MODULE_20__.RiskAnalysisSheet, {\n                open: open,\n                onOpenChange: (isOpen)=>{\n                    if (onOpenChange) {\n                        onOpenChange(isOpen);\n                    }\n                },\n                onSidebarClose: ()=>{\n                    onSidebarClose();\n                    if (onOpenChange) {\n                        onOpenChange(false);\n                    }\n                },\n                title: \"Risk Analysis\",\n                subtitle: \"Towing\",\n                checkFields: fields,\n                riskFactors: (riskAnalysis === null || riskAnalysis === void 0 ? void 0 : (_riskAnalysis_riskFactors = riskAnalysis.riskFactors) === null || _riskAnalysis_riskFactors === void 0 ? void 0 : _riskAnalysis_riskFactors.nodes) || [],\n                crewMembers: members ? members.map((m)=>({\n                        ...m,\n                        value: String(m.value)\n                    })) : [],\n                selectedAuthor: selectedAuthor,\n                onAuthorChange: (value)=>{\n                    setSelectedAuthor(value);\n                    if (value) {\n                        updateRiskAnalysisMember(value.value);\n                    }\n                },\n                canEdit: editTaskingRisk && edit_risks,\n                canDeleteRisks: editTaskingRisk && delete_risks,\n                onRiskClick: (risk)=>{\n                    if (!editTaskingRisk || !edit_risks) {\n                        toast({\n                            title: \"Permission Error\",\n                            description: \"You do not have permission to edit this section\",\n                            variant: \"destructive\"\n                        });\n                        return;\n                    }\n                    handleSetRiskValue(risk);\n                    setCurrentRisk(risk);\n                    setOpenRiskDialog(true);\n                },\n                onAddRiskClick: ()=>{\n                    if (!editTaskingRisk || !edit_risks) {\n                        toast({\n                            title: \"Permission Error\",\n                            description: \"You do not have permission to edit this section\",\n                            variant: \"destructive\"\n                        });\n                        return;\n                    }\n                    setCurrentRisk({});\n                    setContent(\"\");\n                    setRiskValue(null);\n                    setOpenRiskDialog(true);\n                },\n                onRiskDelete: ()=>{\n                    handleDeleteRisk();\n                },\n                setAllChecked: setAllChecked\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 997,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_risk_analysis__WEBPACK_IMPORTED_MODULE_20__.RiskDialog, {\n                open: openRiskDialog,\n                onOpenChange: setOpenRiskDialog,\n                onSave: handleSaveRisk,\n                currentRisk: currentRisk,\n                riskOptions: allRisks || [],\n                riskValue: riskValue,\n                onRiskValueChange: handleRiskValue,\n                riskImpacts: riskImpacts,\n                onRiskImpactChange: (value)=>setCurrentRisk({\n                        ...currentRisk,\n                        impact: value === null || value === void 0 ? void 0 : value.value\n                    }),\n                onRiskProbabilityChange: (value)=>setCurrentRisk({\n                        ...currentRisk,\n                        probability: value\n                    }),\n                currentStrategies: currentStrategies,\n                content: content,\n                onAddStrategyClick: ()=>setOpenRecommendedstrategy(true)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 1066,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_17__.AlertDialogNew, {\n                openDialog: openRecommendedstrategy,\n                setOpenDialog: setOpenRecommendedstrategy,\n                handleCreate: handleNewStrategy,\n                title: \"Recommended strategy\",\n                actionText: \"Save\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"my-2 flex items-center gap-4 flex-wrap\",\n                        children: recommendedStratagies ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                recommendedStratagies.map((risk)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                                        onClick: ()=>{\n                                            setRecommendedstrategy(risk);\n                                            handleSetCurrentStrategies(risk);\n                                            setCurrentRisk({\n                                                ...currentRisk,\n                                                mitigationStrategy: risk\n                                            });\n                                            setUpdateStrategy(false);\n                                        },\n                                        variant: \"outline\",\n                                        className: \"\".concat((currentStrategies === null || currentStrategies === void 0 ? void 0 : currentStrategies.find((s)=>s.id === risk.id)) ? \"border-orange-400 bg-orange-50\" : \"\", \" p-4 rounded-lg cursor-pointer\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            dangerouslySetInnerHTML: {\n                                                __html: risk === null || risk === void 0 ? void 0 : risk.strategy\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                                            lineNumber: 1120,\n                                            columnNumber: 37\n                                        }, this)\n                                    }, risk.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                                        lineNumber: 1101,\n                                        columnNumber: 33\n                                    }, this)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_19__.H4, {\n                                    className: \"leading-6\",\n                                    children: \"or add new Mitigation strategy\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                                    lineNumber: 1126,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: \"No recommendations available!\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                                    lineNumber: 1132,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_19__.H4, {\n                                    className: \"leading-6 mb-4\",\n                                    children: \"Create a new strategy instead\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                                    lineNumber: 1133,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 1097,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_editor__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            id: \"strategy\",\n                            placeholder: \"Mitigation strategy\",\n                            className: \"w-full\",\n                            content: content,\n                            handleEditorChange: handleEditorChange\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                            lineNumber: 1140,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 1139,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 1091,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_17__.AlertDialogNew, {\n                openDialog: openDeleteConfirmation,\n                setOpenDialog: setOpenDeleteConfirmation,\n                handleCreate: handleDeleteRisk,\n                title: \"Delete risk analysis!\",\n                actionText: \"Delete\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 1149,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n        lineNumber: 996,\n        columnNumber: 9\n    }, this);\n}\n_s(RiskAnalysis, \"GzlqDmyzpKlhO465I5pfnFzHn78=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useSearchParams,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_21__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_21__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_21__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation\n    ];\n});\n_c = RiskAnalysis;\nvar _c;\n$RefreshReg$(_c, \"RiskAnalysis\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/forms/risk-analysis.tsx\n"));

/***/ })

});