"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/forms/risk-analysis.tsx":
/*!****************************************************!*\
  !*** ./src/app/ui/logbook/forms/risk-analysis.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ RiskAnalysis; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_offline_models_logBookEntry__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/offline/models/logBookEntry */ \"(app-pages-browser)/./src/app/offline/models/logBookEntry.js\");\n/* harmony import */ var _app_offline_models_towingChecklist__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/offline/models/towingChecklist */ \"(app-pages-browser)/./src/app/offline/models/towingChecklist.js\");\n/* harmony import */ var _app_offline_models_riskFactor__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/offline/models/riskFactor */ \"(app-pages-browser)/./src/app/offline/models/riskFactor.js\");\n/* harmony import */ var _app_offline_models_crewMembers_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/offline/models/crewMembers_LogBookEntrySection */ \"(app-pages-browser)/./src/app/offline/models/crewMembers_LogBookEntrySection.js\");\n/* harmony import */ var _app_offline_models_eventType_Tasking__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/offline/models/eventType_Tasking */ \"(app-pages-browser)/./src/app/offline/models/eventType_Tasking.js\");\n/* harmony import */ var _app_offline_models_mitigationStrategy__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/offline/models/mitigationStrategy */ \"(app-pages-browser)/./src/app/offline/models/mitigationStrategy.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var lodash_uniqBy__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! lodash/uniqBy */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/uniqBy.js\");\n/* harmony import */ var lodash_uniqBy__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(lodash_uniqBy__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var _components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/alert-dialog-new */ \"(app-pages-browser)/./src/components/ui/alert-dialog-new.tsx\");\n/* harmony import */ var _components_ui_risk_analysis__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/risk-analysis */ \"(app-pages-browser)/./src/components/ui/risk-analysis/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n// Import React and hooks\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Risk Analysis components\n\nfunction RiskAnalysis(param) {\n    let { selectedEvent = false, onSidebarClose, logBookConfig, currentTrip, crewMembers = false, towingChecklistID = 0, setTowingChecklistID, offline = false, setAllChecked, open = false, onOpenChange } = param;\n    var _riskAnalysis_riskFactors;\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams)();\n    var _searchParams_get;\n    const logentryID = parseInt((_searchParams_get = searchParams.get(\"logentryID\")) !== null && _searchParams_get !== void 0 ? _searchParams_get : \"0\");\n    var _searchParams_get1;\n    const vesselID = (_searchParams_get1 = searchParams.get(\"vesselID\")) !== null && _searchParams_get1 !== void 0 ? _searchParams_get1 : 0;\n    const [riskAnalysis, setRiskAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [riskBuffer, setRiskBuffer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openRiskDialog, setOpenRiskDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentRisk, setCurrentRisk] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [allRisks, setAllRisks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [allRiskFactors, setAllRiskFactors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [riskValue, setRiskValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Using setUpdateStrategy but not reading updateStrategy\n    const [, setUpdateStrategy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Unused state variables commented out\n    // const [strategyEditor, setstrategyEditor] = useState<any>(false)\n    const [openRecommendedstrategy, setOpenRecommendedstrategy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [recommendedStratagies, setRecommendedStratagies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentStrategies, setCurrentStrategies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Using setRecommendedstrategy but not reading recommendedstrategy\n    const [, setRecommendedstrategy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [riskToDelete, setRiskToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openDeleteConfirmation, setOpenDeleteConfirmation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [logbook, setLogbook] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [members, setMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [edit_risks, setEdit_risks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [delete_risks, setDelete_risks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editTaskingRisk, setEditTaskingRisk] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast)();\n    const logBookEntryModel = new _app_offline_models_logBookEntry__WEBPACK_IMPORTED_MODULE_8__[\"default\"]();\n    const towingChecklistModel = new _app_offline_models_towingChecklist__WEBPACK_IMPORTED_MODULE_9__[\"default\"]();\n    const riskFactorModel = new _app_offline_models_riskFactor__WEBPACK_IMPORTED_MODULE_10__[\"default\"]();\n    const crewMemberModel = new _app_offline_models_crewMembers_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_11__[\"default\"]();\n    const taskingModel = new _app_offline_models_eventType_Tasking__WEBPACK_IMPORTED_MODULE_12__[\"default\"]();\n    const mitigationStrategyModel = new _app_offline_models_mitigationStrategy__WEBPACK_IMPORTED_MODULE_13__[\"default\"]();\n    const [selectedAuthor, setSelectedAuthor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const init_permissions = ()=>{\n        if (permissions) {\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_7__.hasPermission)(\"EDIT_RISK\", permissions)) {\n                setEdit_risks(true);\n            } else {\n                setEdit_risks(false);\n            }\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_7__.hasPermission)(\"DELETE_RISK\", permissions)) {\n                setDelete_risks(true);\n            } else {\n                setDelete_risks(false);\n            }\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_7__.hasPermission)(\"EDIT_LOGBOOKENTRY_RISK_ANALYSIS\", permissions)) {\n                setEditTaskingRisk(true);\n            } else {\n                setEditTaskingRisk(false);\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_7__.getPermissions);\n        init_permissions();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        init_permissions();\n    }, [\n        permissions\n    ]);\n    const [getSectionCrewMembers_LogBookEntrySection, // Unused loading state\n    {}] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__.CrewMembers_LogBookEntrySection, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            let data = response.readCrewMembers_LogBookEntrySections.nodes;\n            const crewMembers = data.map((member)=>{\n                var _member_crewMember_firstName, _member_crewMember_surname;\n                return {\n                    label: \"\".concat((_member_crewMember_firstName = member.crewMember.firstName) !== null && _member_crewMember_firstName !== void 0 ? _member_crewMember_firstName : \"\", \" \").concat((_member_crewMember_surname = member.crewMember.surname) !== null && _member_crewMember_surname !== void 0 ? _member_crewMember_surname : \"\"),\n                    value: member.crewMember.id\n                };\n            }).filter((member)=>member.value != logbook.master.id);\n            setMembers(lodash_uniqBy__WEBPACK_IMPORTED_MODULE_15___default()([\n                ...members,\n                ...crewMembers\n            ], \"value\"));\n        },\n        onError: (error)=>{\n            console.error(\"CrewMembers_LogBookEntrySection error\", error);\n        }\n    });\n    const handleSetLogbook = async (logbook)=>{\n        setLogbook(logbook);\n        var _logbook_master_firstName, _logbook_master_surname;\n        const master = {\n            label: \"\".concat((_logbook_master_firstName = logbook.master.firstName) !== null && _logbook_master_firstName !== void 0 ? _logbook_master_firstName : \"\", \" \").concat((_logbook_master_surname = logbook.master.surname) !== null && _logbook_master_surname !== void 0 ? _logbook_master_surname : \"\"),\n            value: logbook.master.id\n        };\n        if (+master.value > 0) {\n            if (Array.isArray(members)) {\n                setMembers(lodash_uniqBy__WEBPACK_IMPORTED_MODULE_15___default()([\n                    ...members,\n                    master\n                ], \"value\"));\n            } else {\n                setMembers([\n                    master\n                ]);\n            }\n        }\n        const sections = logbook.logBookEntrySections.nodes.filter((node)=>{\n            return node.className === \"SeaLogs\\\\CrewMembers_LogBookEntrySection\";\n        });\n        if (sections) {\n            const sectionIDs = sections.map((section)=>section.id);\n            if ((sectionIDs === null || sectionIDs === void 0 ? void 0 : sectionIDs.length) > 0) {\n                if (offline) {\n                    const data = await crewMemberModel.getByIds(sectionIDs);\n                    const crewMembers = data.map((member)=>{\n                        var _member_crewMember_firstName, _member_crewMember_surname;\n                        return {\n                            label: \"\".concat((_member_crewMember_firstName = member.crewMember.firstName) !== null && _member_crewMember_firstName !== void 0 ? _member_crewMember_firstName : \"\", \" \").concat((_member_crewMember_surname = member.crewMember.surname) !== null && _member_crewMember_surname !== void 0 ? _member_crewMember_surname : \"\"),\n                            value: member.crewMember.id\n                        };\n                    });\n                    if (Array.isArray(members)) {\n                        setMembers(lodash_uniqBy__WEBPACK_IMPORTED_MODULE_15___default()([\n                            ...members,\n                            ...crewMembers\n                        ], \"value\"));\n                    } else {\n                        setMembers(crewMembers);\n                    }\n                } else {\n                    getSectionCrewMembers_LogBookEntrySection({\n                        variables: {\n                            filter: {\n                                id: {\n                                    in: sectionIDs\n                                }\n                            }\n                        }\n                    });\n                }\n            }\n        }\n    };\n    if (logentryID > 0 && !offline) {\n        (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_6__.getLogBookEntryByID)(+logentryID, handleSetLogbook);\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (crewMembers) {\n            const members = crewMembers.map((member)=>{\n                var _member_crewMember_firstName, _member_crewMember_surname;\n                return {\n                    label: \"\".concat((_member_crewMember_firstName = member.crewMember.firstName) !== null && _member_crewMember_firstName !== void 0 ? _member_crewMember_firstName : \"\", \" \").concat((_member_crewMember_surname = member.crewMember.surname) !== null && _member_crewMember_surname !== void 0 ? _member_crewMember_surname : \"\"),\n                    value: member.crewMemberID\n                };\n            });\n            setMembers(members);\n        }\n    }, [\n        crewMembers\n    ]);\n    const handleTaskingRiskFieldChange = (field)=>async (check)=>{\n            if (!editTaskingRisk || !edit_risks) {\n                toast({\n                    title: \"Permission Error\",\n                    description: \"You do not have permission to edit this section\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            setRiskBuffer({\n                ...riskBuffer,\n                [field]: check ? \"on\" : \"off\"\n            });\n            if (+(riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.id) > 0) {\n                if (offline) {\n                    const data = await towingChecklistModel.save({\n                        id: riskAnalysis.id,\n                        [field]: check ? true : false\n                    });\n                    const towingChecklistData = await towingChecklistModel.getById(data.id);\n                    setRiskAnalysis(towingChecklistData);\n                } else {\n                    updateTowingChecklist({\n                        variables: {\n                            input: {\n                                id: riskAnalysis.id,\n                                [field]: check ? true : false\n                            }\n                        }\n                    });\n                }\n            }\n        };\n    const [updateTowingChecklist] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_19__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.UpdateTowingChecklist, {\n        onCompleted: (data)=>{\n            getRiskAnalysis({\n                variables: {\n                    id: data.updateTowingChecklist.id\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const fields = [\n        {\n            name: \"ConductSAP\",\n            label: \"Conduct SAP\",\n            value: \"conductSAP\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.conductSAP) ? riskBuffer.conductSAP === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.conductSAP,\n            handleChange: handleTaskingRiskFieldChange(\"conductSAP\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Conduct SAP prior to approaching the vessel.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 308,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Check for fittings on the vessel that could damage the CRV when coming alongside.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 307,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            name: \"InvestigateNatureOfIssue\",\n            label: \"Investigate nature of the issue\",\n            value: \"investigateNatureOfIssue\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.investigateNatureOfIssue) ? riskBuffer.investigateNatureOfIssue === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.investigateNatureOfIssue,\n            handleChange: handleTaskingRiskFieldChange(\"investigateNatureOfIssue\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Ascertain the nature of the problem, any damage, or taking on water.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 328,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Does a crew member need to go on board the other vessel to assist?\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 327,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            name: \"EveryoneOnBoardOk\",\n            label: \"Everyone on board ok?\",\n            value: \"everyoneOnBoardOk\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.everyoneOnBoardOk) ? riskBuffer.everyoneOnBoardOk === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.everyoneOnBoardOk,\n            handleChange: handleTaskingRiskFieldChange(\"everyoneOnBoardOk\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Check how many people are aboard, ensure everyone is accounted for.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 349,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Check for injuries or medical assistance required.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 348,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            name: \"RudderToMidshipsAndTrimmed\",\n            label: \"Rudder to midships and trimmed appropriately\",\n            value: \"rudderToMidshipsAndTrimmed\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.rudderToMidshipsAndTrimmed) ? riskBuffer.rudderToMidshipsAndTrimmed === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.rudderToMidshipsAndTrimmed,\n            handleChange: handleTaskingRiskFieldChange(\"rudderToMidshipsAndTrimmed\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Check steering isn’t impaired in any way and have the rudder secured amidships or have the vessel steer for the stern of CRV.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 371,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Check the vessel is optimally trimmed for towing.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 376,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 370,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            name: \"LifejacketsOn\",\n            label: \"Lifejackets on\",\n            value: \"lifejacketsOn\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.lifejacketsOn) ? riskBuffer.lifejacketsOn === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.lifejacketsOn,\n            handleChange: handleTaskingRiskFieldChange(\"lifejacketsOn\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: \"Request that everyone wears a lifejacket.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                    lineNumber: 390,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 389,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            name: \"CommunicationsEstablished\",\n            label: \"Communications Established\",\n            value: \"communicationsEstablished\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.communicationsEstablished) ? riskBuffer.communicationsEstablished === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.communicationsEstablished,\n            handleChange: handleTaskingRiskFieldChange(\"communicationsEstablished\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Ensure that communications have been established and checked prior to beginning the tow, i.e., VHF, hand signals, and/or light signals if the tow is to be conducted at night.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 406,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Ensure there is agreement on where to tow the vessel to.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 412,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 405,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            name: \"SecureAndSafeTowing\",\n            label: \"Secure and safe towing\",\n            value: \"secureAndSafeTowing\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.secureAndSafeTowing) ? riskBuffer.secureAndSafeTowing === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.secureAndSafeTowing,\n            handleChange: handleTaskingRiskFieldChange(\"secureAndSafeTowing\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Towline securely attached\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 428,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Ensure everything on board is stowed and secure.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 429,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Confirm waterline length/cruising speed of the vessel (safe tow speed).\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 430,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Confirm attachment points for the towline.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 434,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Confirm that the towline is securely attached.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 435,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Ensure that no one on the other vessel is in close proximity to the towline before commencing the tow.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 436,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Turn on CRV towing lights and other vessel’s navigation lights.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 440,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Post towline lookout with responsibility for quick release of the tow / must carry or have a knife handy.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 444,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 427,\n                columnNumber: 17\n            }, this)\n        }\n    ];\n    const createOfflineTowingChecklist = async ()=>{\n        var _selectedEvent_eventType_Tasking;\n        const data = await towingChecklistModel.save({\n            id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)()\n        });\n        setTowingChecklistID(+data.id);\n        await taskingModel.save({\n            id: towingChecklistID > 0 ? towingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking === void 0 ? void 0 : _selectedEvent_eventType_Tasking.id,\n            towingChecklistID: +data.id\n        });\n        const towingChecklistData = await towingChecklistModel.getById(data.id);\n        setRiskAnalysis(towingChecklistData);\n    };\n    const offlineGetRiskAnalysis = async ()=>{\n        var _selectedEvent_eventType_Tasking_towingChecklist, _selectedEvent_eventType_Tasking;\n        const data = await towingChecklistModel.getById(towingChecklistID > 0 ? towingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking === void 0 ? void 0 : (_selectedEvent_eventType_Tasking_towingChecklist = _selectedEvent_eventType_Tasking.towingChecklist) === null || _selectedEvent_eventType_Tasking_towingChecklist === void 0 ? void 0 : _selectedEvent_eventType_Tasking_towingChecklist.id);\n        setRiskAnalysis(data);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedEvent || towingChecklistID > 0) {\n            var _selectedEvent_eventType_Tasking_towingChecklist, _selectedEvent_eventType_Tasking;\n            if ((selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking === void 0 ? void 0 : (_selectedEvent_eventType_Tasking_towingChecklist = _selectedEvent_eventType_Tasking.towingChecklist) === null || _selectedEvent_eventType_Tasking_towingChecklist === void 0 ? void 0 : _selectedEvent_eventType_Tasking_towingChecklist.id) > 0 || towingChecklistID > 0) {\n                if (offline) {\n                    offlineGetRiskAnalysis();\n                } else {\n                    var _selectedEvent_eventType_Tasking_towingChecklist1, _selectedEvent_eventType_Tasking1;\n                    getRiskAnalysis({\n                        variables: {\n                            id: towingChecklistID > 0 ? towingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking1 = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking1 === void 0 ? void 0 : (_selectedEvent_eventType_Tasking_towingChecklist1 = _selectedEvent_eventType_Tasking1.towingChecklist) === null || _selectedEvent_eventType_Tasking_towingChecklist1 === void 0 ? void 0 : _selectedEvent_eventType_Tasking_towingChecklist1.id\n                        }\n                    });\n                }\n            } else {\n                if (offline) {\n                    createOfflineTowingChecklist();\n                } else {\n                    createTowingChecklist({\n                        variables: {\n                            input: {}\n                        }\n                    });\n                }\n            }\n        }\n    }, [\n        selectedEvent,\n        towingChecklistID\n    ]);\n    const offlineMount = async ()=>{\n        var _Array_from;\n        const data = await riskFactorModel.getByFieldID(\"type\", \"TowingChecklist\");\n        const risks = (_Array_from = Array.from(new Set(data.map((risk)=>risk.title)))) === null || _Array_from === void 0 ? void 0 : _Array_from.map((risk)=>({\n                label: risk,\n                value: risk\n            }));\n        setAllRisks(risks);\n        setAllRiskFactors(data);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (offline) {\n            offlineMount();\n        } else {\n            getRiskFactors({\n                variables: {\n                    filter: {\n                        type: {\n                            eq: \"TowingChecklist\"\n                        }\n                    }\n                }\n            });\n        }\n    }, []);\n    const [getRiskFactors] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__.GetRiskFactors, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            var _Array_from, _data_readRiskFactors_nodes;\n            const risks = (_Array_from = Array.from(new Set((_data_readRiskFactors_nodes = data.readRiskFactors.nodes) === null || _data_readRiskFactors_nodes === void 0 ? void 0 : _data_readRiskFactors_nodes.map((risk)=>risk.title)))) === null || _Array_from === void 0 ? void 0 : _Array_from.map((risk)=>({\n                    label: risk,\n                    value: risk\n                }));\n            setAllRisks(risks);\n            setAllRiskFactors(data.readRiskFactors.nodes);\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [getRiskAnalysis] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__.TowingChecklist, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            setRiskAnalysis(data.readOneTowingChecklist);\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [createTowingChecklist] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_19__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.CreateTowingChecklist, {\n        onCompleted: (data)=>{\n            var _selectedEvent_eventType_Tasking;\n            setTowingChecklistID(+data.createTowingChecklist.id);\n            updateEvent({\n                variables: {\n                    input: {\n                        id: towingChecklistID > 0 ? towingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking === void 0 ? void 0 : _selectedEvent_eventType_Tasking.id,\n                        towingChecklistID: +data.createTowingChecklist.id\n                    }\n                }\n            });\n            getRiskAnalysis({\n                variables: {\n                    id: data.createTowingChecklist.id\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [updateEvent] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_19__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.UpdateEventType_Tasking, {\n        onCompleted: ()=>{},\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const updateRiskAnalysisMember = async (memberID)=>{\n        if (!editTaskingRisk || !edit_risks) {\n            toast({\n                title: \"Permission Error\",\n                description: \"You do not have permission to edit this section\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        if (offline) {\n            const data = await towingChecklistModel.save({\n                id: riskAnalysis.id,\n                memberID: memberID\n            });\n            const towingChecklistData = await towingChecklistModel.getById(data.id);\n            setRiskAnalysis(towingChecklistData);\n        } else {\n            updateTowingChecklist({\n                variables: {\n                    input: {\n                        id: riskAnalysis.id,\n                        memberID: memberID\n                    }\n                }\n            });\n        }\n    };\n    const handleEditorChange = (newContent)=>{\n        setContent(newContent);\n    };\n    const riskImpacts = [\n        {\n            value: \"Low\",\n            label: \"Low impact\"\n        },\n        {\n            value: \"Medium\",\n            label: \"Medium impact\"\n        },\n        {\n            value: \"High\",\n            label: \"High impact\"\n        },\n        {\n            value: \"Severe\",\n            label: \"Severe impact\"\n        }\n    ];\n    const handleSaveRisk = async ()=>{\n        if (currentRisk.id > 0) {\n            if (offline) {\n                var _Array_from, _selectedEvent_eventType_Tasking_towingChecklist, _selectedEvent_eventType_Tasking;\n                await riskFactorModel.save({\n                    id: currentRisk.id,\n                    type: \"TowingChecklist\",\n                    title: currentRisk.title,\n                    impact: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact : \"Low\",\n                    probability: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability : 5,\n                    mitigationStrategy: currentStrategies.length > 0 ? currentStrategies.map((s)=>s.id).join(\",\") : \"\",\n                    towingChecklistID: riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.id\n                });\n                setOpenRiskDialog(false);\n                const data = await riskFactorModel.getByFieldID(\"type\", \"TowingChecklist\");\n                const risks = (_Array_from = Array.from(new Set(data.map((risk)=>risk.title)))) === null || _Array_from === void 0 ? void 0 : _Array_from.map((risk)=>({\n                        label: risk,\n                        value: risk\n                    }));\n                setAllRisks(risks);\n                setAllRiskFactors(data);\n                const towingChecklistData = await towingChecklistModel.getById(towingChecklistID > 0 ? towingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking === void 0 ? void 0 : (_selectedEvent_eventType_Tasking_towingChecklist = _selectedEvent_eventType_Tasking.towingChecklist) === null || _selectedEvent_eventType_Tasking_towingChecklist === void 0 ? void 0 : _selectedEvent_eventType_Tasking_towingChecklist.id);\n                setRiskAnalysis(towingChecklistData);\n            } else {\n                updateRiskFactor({\n                    variables: {\n                        input: {\n                            id: currentRisk.id,\n                            type: \"TowingChecklist\",\n                            title: currentRisk.title,\n                            impact: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact : \"Low\",\n                            probability: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability : 5,\n                            mitigationStrategy: currentStrategies.length > 0 ? currentStrategies.map((s)=>s.id).join(\",\") : \"\",\n                            towingChecklistID: riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.id\n                        }\n                    }\n                });\n            }\n        } else {\n            if (offline) {\n                var _Array_from1, _selectedEvent_eventType_Tasking_towingChecklist1, _selectedEvent_eventType_Tasking1;\n                await riskFactorModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)(),\n                    type: \"TowingChecklist\",\n                    title: currentRisk.title,\n                    impact: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact : \"Low\",\n                    probability: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability : 5,\n                    mitigationStrategy: currentStrategies.length > 0 ? currentStrategies.map((s)=>s.id).join(\",\") : \"\",\n                    towingChecklistID: riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.id,\n                    vesselID: vesselID\n                });\n                setOpenRiskDialog(false);\n                const data = await riskFactorModel.getByFieldID(\"type\", \"TowingChecklist\");\n                const risks = (_Array_from1 = Array.from(new Set(data.map((risk)=>risk.title)))) === null || _Array_from1 === void 0 ? void 0 : _Array_from1.map((risk)=>({\n                        label: risk,\n                        value: risk\n                    }));\n                setAllRisks(risks);\n                setAllRiskFactors(data);\n                const towingChecklistData = await towingChecklistModel.getById(towingChecklistID > 0 ? towingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking1 = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking1 === void 0 ? void 0 : (_selectedEvent_eventType_Tasking_towingChecklist1 = _selectedEvent_eventType_Tasking1.towingChecklist) === null || _selectedEvent_eventType_Tasking_towingChecklist1 === void 0 ? void 0 : _selectedEvent_eventType_Tasking_towingChecklist1.id);\n                setRiskAnalysis(towingChecklistData);\n            } else {\n                createRiskFactor({\n                    variables: {\n                        input: {\n                            type: \"TowingChecklist\",\n                            title: currentRisk.title,\n                            impact: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact : \"Low\",\n                            probability: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability : 5,\n                            mitigationStrategy: currentStrategies.length > 0 ? currentStrategies.map((s)=>s.id).join(\",\") : \"\",\n                            towingChecklistID: riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.id,\n                            vesselID: vesselID\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const [createMitigationStrategy] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_19__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.CreateMitigationStrategy, {\n        onCompleted: (data)=>{\n            setCurrentStrategies([\n                ...currentStrategies,\n                {\n                    id: data.createMitigationStrategy.id,\n                    strategy: content\n                }\n            ]);\n            setContent(\"\");\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [createRiskFactor] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_19__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.CreateRiskFactor, {\n        onCompleted: ()=>{\n            var _selectedEvent_eventType_Tasking_towingChecklist, _selectedEvent_eventType_Tasking;\n            setOpenRiskDialog(false);\n            getRiskFactors({\n                variables: {\n                    filter: {\n                        type: {\n                            eq: \"TowingChecklist\"\n                        }\n                    }\n                }\n            });\n            getRiskAnalysis({\n                variables: {\n                    id: towingChecklistID > 0 ? towingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking === void 0 ? void 0 : (_selectedEvent_eventType_Tasking_towingChecklist = _selectedEvent_eventType_Tasking.towingChecklist) === null || _selectedEvent_eventType_Tasking_towingChecklist === void 0 ? void 0 : _selectedEvent_eventType_Tasking_towingChecklist.id\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [updateRiskFactor] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_19__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.UpdateRiskFactor, {\n        onCompleted: ()=>{\n            var _selectedEvent_eventType_Tasking_towingChecklist, _selectedEvent_eventType_Tasking;\n            setOpenRiskDialog(false);\n            getRiskFactors({\n                variables: {\n                    filter: {\n                        type: {\n                            eq: \"TowingChecklist\"\n                        }\n                    }\n                }\n            });\n            getRiskAnalysis({\n                variables: {\n                    id: towingChecklistID > 0 ? towingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking === void 0 ? void 0 : (_selectedEvent_eventType_Tasking_towingChecklist = _selectedEvent_eventType_Tasking.towingChecklist) === null || _selectedEvent_eventType_Tasking_towingChecklist === void 0 ? void 0 : _selectedEvent_eventType_Tasking_towingChecklist.id\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const handleRiskValue = (v)=>{\n        setCurrentRisk({\n            ...currentRisk,\n            title: v === null || v === void 0 ? void 0 : v.value\n        });\n        setRiskValue({\n            value: v.value,\n            label: v.value\n        });\n        if ((allRiskFactors === null || allRiskFactors === void 0 ? void 0 : allRiskFactors.filter((risk)=>{\n            var _risk_mitigationStrategy_nodes;\n            return risk.title === v.value && ((_risk_mitigationStrategy_nodes = risk.mitigationStrategy.nodes) === null || _risk_mitigationStrategy_nodes === void 0 ? void 0 : _risk_mitigationStrategy_nodes.length) > 0;\n        }).length) > 0) {\n            setRecommendedStratagies(Array.from(new Set(allRiskFactors === null || allRiskFactors === void 0 ? void 0 : allRiskFactors.filter((r)=>{\n                var _r_mitigationStrategy_nodes;\n                return r.title === v.value && ((_r_mitigationStrategy_nodes = r.mitigationStrategy.nodes) === null || _r_mitigationStrategy_nodes === void 0 ? void 0 : _r_mitigationStrategy_nodes.length) > 0;\n            }).map((r)=>r.mitigationStrategy.nodes)[0].map((s)=>({\n                    id: s.id,\n                    strategy: s.strategy\n                })))));\n        } else {\n            setRecommendedStratagies(false);\n        }\n    };\n    // This function is not used directly in the component but is kept for reference\n    // and potential future use\n    // const handleCreateRisk = (inputValue: any) => {\n    //     setCurrentRisk({\n    //         ...currentRisk,\n    //         title: inputValue,\n    //     })\n    //     setRiskValue({ value: inputValue, label: inputValue })\n    //     if (allRisks) {\n    //         const risk = [...allRisks, { value: inputValue, label: inputValue }]\n    //         setAllRisks(risk)\n    //     } else {\n    //         setAllRisks([{ value: inputValue, label: inputValue }])\n    //     }\n    // }\n    const handleDeleteRisk = async ()=>{\n        if (offline) {\n            var _Array_from, _selectedEvent_eventType_Tasking_towingChecklist, _selectedEvent_eventType_Tasking;\n            await riskFactorModel.save({\n                id: riskToDelete.id,\n                towingChecklistID: 0,\n                vesselID: 0\n            });\n            setOpenRiskDialog(false);\n            const data = await riskFactorModel.getByFieldID(\"type\", \"TowingChecklist\");\n            const risks = (_Array_from = Array.from(new Set(data.map((risk)=>risk.title)))) === null || _Array_from === void 0 ? void 0 : _Array_from.map((risk)=>({\n                    label: risk,\n                    value: risk\n                }));\n            setAllRisks(risks);\n            setAllRiskFactors(data);\n            const towingChecklistData = await towingChecklistModel.getById(towingChecklistID > 0 ? towingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking === void 0 ? void 0 : (_selectedEvent_eventType_Tasking_towingChecklist = _selectedEvent_eventType_Tasking.towingChecklist) === null || _selectedEvent_eventType_Tasking_towingChecklist === void 0 ? void 0 : _selectedEvent_eventType_Tasking_towingChecklist.id);\n            setRiskAnalysis(towingChecklistData);\n        } else {\n            updateRiskFactor({\n                variables: {\n                    input: {\n                        id: riskToDelete.id,\n                        towingChecklistID: 0,\n                        vesselID: 0\n                    }\n                }\n            });\n        }\n        setOpenDeleteConfirmation(false);\n    };\n    const handleSetCurrentStrategies = (strategy)=>{\n        if (currentStrategies.length > 0) {\n            if (currentStrategies.find((s)=>s.id === strategy.id)) {\n                setCurrentStrategies(currentStrategies.filter((s)=>s.id !== strategy.id));\n            } else {\n                setCurrentStrategies([\n                    ...currentStrategies,\n                    strategy\n                ]);\n            }\n        } else {\n            setCurrentStrategies([\n                strategy\n            ]);\n        }\n    };\n    const handleNewStrategy = async ()=>{\n        if (content) {\n            if (offline) {\n                const data = await mitigationStrategyModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)(),\n                    strategy: content\n                });\n                const newStrategies = [\n                    ...currentStrategies,\n                    {\n                        id: data.id,\n                        strategy: content\n                    }\n                ];\n                setCurrentRisk({\n                    ...currentRisk,\n                    mitigationStrategy: {\n                        nodes: newStrategies\n                    }\n                });\n                setCurrentStrategies(newStrategies);\n                setContent(\"\");\n            } else {\n                createMitigationStrategy({\n                    variables: {\n                        input: {\n                            strategy: content\n                        }\n                    }\n                });\n            }\n        }\n        setOpenRecommendedstrategy(false);\n    };\n    const handleSetRiskValue = (v)=>{\n        setRiskValue({\n            value: v.title,\n            label: v.title\n        });\n        if (v.mitigationStrategy.nodes) {\n            setCurrentStrategies(v.mitigationStrategy.nodes);\n        }\n        if ((allRiskFactors === null || allRiskFactors === void 0 ? void 0 : allRiskFactors.filter((risk)=>{\n            var _risk_mitigationStrategy_nodes;\n            return risk.title === v.title && ((_risk_mitigationStrategy_nodes = risk.mitigationStrategy.nodes) === null || _risk_mitigationStrategy_nodes === void 0 ? void 0 : _risk_mitigationStrategy_nodes.length) > 0;\n        }).length) > 0) {\n            setRecommendedStratagies(Array.from(new Set(allRiskFactors === null || allRiskFactors === void 0 ? void 0 : allRiskFactors.filter((r)=>{\n                var _r_mitigationStrategy_nodes;\n                return r.title === v.title && ((_r_mitigationStrategy_nodes = r.mitigationStrategy.nodes) === null || _r_mitigationStrategy_nodes === void 0 ? void 0 : _r_mitigationStrategy_nodes.length) > 0;\n            }).map((r)=>r.mitigationStrategy.nodes)[0].map((s)=>({\n                    id: s.id,\n                    strategy: s.strategy\n                })))));\n        } else {\n            setRecommendedStratagies(false);\n        }\n    };\n    const offlineGetLogBookEntryByID = async ()=>{\n        const logbook = await logBookEntryModel.getById(logentryID);\n        handleSetLogbook(logbook);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (offline) {\n            offlineGetLogBookEntryByID();\n        }\n    }, [\n        offline\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (members && riskAnalysis) {\n            const member = members.find((member)=>member.value == riskAnalysis.member.id);\n            setSelectedAuthor(member);\n        }\n    }, [\n        members,\n        riskAnalysis\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setAllChecked(fields.every((field)=>field.checked));\n    }, [\n        fields\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_risk_analysis__WEBPACK_IMPORTED_MODULE_17__.RiskAnalysisSheet, {\n                open: open,\n                onOpenChange: (isOpen)=>{\n                    if (onOpenChange) {\n                        onOpenChange(isOpen);\n                    }\n                },\n                onSidebarClose: ()=>{\n                    onSidebarClose();\n                    if (onOpenChange) {\n                        onOpenChange(false);\n                    }\n                },\n                title: \"Risk Analysis\",\n                subtitle: \"Towing\",\n                checkFields: fields,\n                riskFactors: (riskAnalysis === null || riskAnalysis === void 0 ? void 0 : (_riskAnalysis_riskFactors = riskAnalysis.riskFactors) === null || _riskAnalysis_riskFactors === void 0 ? void 0 : _riskAnalysis_riskFactors.nodes) || [],\n                crewMembers: members ? members.map((m)=>({\n                        ...m,\n                        value: String(m.value)\n                    })) : [],\n                selectedAuthor: selectedAuthor,\n                onAuthorChange: (value)=>{\n                    setSelectedAuthor(value);\n                    if (value) {\n                        updateRiskAnalysisMember(value.value);\n                    }\n                },\n                canEdit: editTaskingRisk && edit_risks,\n                canDeleteRisks: editTaskingRisk && delete_risks,\n                onRiskClick: (risk)=>{\n                    if (!editTaskingRisk || !edit_risks) {\n                        toast({\n                            title: \"Permission Error\",\n                            description: \"You do not have permission to edit this section\",\n                            variant: \"destructive\"\n                        });\n                        return;\n                    }\n                    handleSetRiskValue(risk);\n                    setCurrentRisk(risk);\n                    setOpenRiskDialog(true);\n                },\n                onAddRiskClick: ()=>{\n                    if (!editTaskingRisk || !edit_risks) {\n                        toast({\n                            title: \"Permission Error\",\n                            description: \"You do not have permission to edit this section\",\n                            variant: \"destructive\"\n                        });\n                        return;\n                    }\n                    setCurrentRisk({});\n                    setContent(\"\");\n                    setRiskValue(null);\n                    setOpenRiskDialog(true);\n                },\n                onRiskDelete: ()=>{\n                    handleDeleteRisk();\n                },\n                setAllChecked: setAllChecked\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 997,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_risk_analysis__WEBPACK_IMPORTED_MODULE_17__.RiskDialog, {\n                open: openRiskDialog,\n                onOpenChange: setOpenRiskDialog,\n                onSave: handleSaveRisk,\n                currentRisk: currentRisk,\n                riskOptions: allRisks || [],\n                riskValue: riskValue,\n                onRiskValueChange: handleRiskValue,\n                riskImpacts: riskImpacts,\n                onRiskImpactChange: (value)=>setCurrentRisk({\n                        ...currentRisk,\n                        impact: value === null || value === void 0 ? void 0 : value.value\n                    }),\n                onRiskProbabilityChange: (value)=>setCurrentRisk({\n                        ...currentRisk,\n                        probability: value\n                    }),\n                currentStrategies: currentStrategies,\n                content: content,\n                onAddStrategyClick: ()=>setOpenRecommendedstrategy(true)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 1066,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_risk_analysis__WEBPACK_IMPORTED_MODULE_17__.StrategyDialog, {\n                open: openRecommendedstrategy,\n                onOpenChange: setOpenRecommendedstrategy,\n                onSave: handleNewStrategy,\n                currentRisk: currentRisk,\n                recommendedStrategies: recommendedStratagies,\n                currentStrategies: currentStrategies,\n                onStrategySelect: (strategy)=>{\n                    setRecommendedstrategy(strategy);\n                    handleSetCurrentStrategies(strategy);\n                    setCurrentRisk({\n                        ...currentRisk,\n                        mitigationStrategy: strategy\n                    });\n                    setUpdateStrategy(false);\n                },\n                content: content,\n                onEditorChange: handleEditorChange\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 1091,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_16__.AlertDialogNew, {\n                openDialog: openDeleteConfirmation,\n                setOpenDialog: setOpenDeleteConfirmation,\n                handleCreate: handleDeleteRisk,\n                title: \"Delete risk analysis!\",\n                actionText: \"Delete\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 1110,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n        lineNumber: 996,\n        columnNumber: 9\n    }, this);\n}\n_s(RiskAnalysis, \"GzlqDmyzpKlhO465I5pfnFzHn78=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_19__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_19__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_19__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_19__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_19__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_19__.useMutation\n    ];\n});\n_c = RiskAnalysis;\nvar _c;\n$RefreshReg$(_c, \"RiskAnalysis\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/forms/risk-analysis.tsx\n"));

/***/ })

});